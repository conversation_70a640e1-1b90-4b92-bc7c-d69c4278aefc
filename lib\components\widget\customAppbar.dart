import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Add this import
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
// Add this import for Help page

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final bool showActionIcon;
  final bool showSearchIcon;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.showActionIcon = true,
    this.showSearchIcon = false,
    this.showBackButton = true,
    this.onBackPressed,
  }) : super(key: key);

  @override
  _CustomAppBarState createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomAppBarState extends State<CustomAppBar> {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.secondaryColor,
      scrolledUnderElevation: 4.0,
      shadowColor: AppColors.viewColor.withOpacity(0.15),
      surfaceTintColor: Colors.transparent,
      centerTitle: true,
      leading:
          widget.showBackButton
              ? IconButton(
                icon: const CustomSvgImage(imageName: 'appbackbutton'),
                onPressed: widget.onBackPressed ?? () => context.go('/dashboard'),
              )
              : null,
      title: DmSansText(
        widget.title,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.viewColor,
      ),
      actions: [
        if (widget.showSearchIcon && !_isSearching)
          IconButton(
            icon: Icon(Icons.search, color: AppColors.viewColor),
            onPressed: () {
              setState(() {
                _isSearching = true;
              });
            },
          ),
        if (widget.showActionIcon && !_isSearching)
          InkWell(
            borderRadius: BorderRadius.circular(20),
            highlightColor: AppColors.microinteraction,
            splashColor: AppColors.microinteraction,
            onTap: () async {
              await Future.delayed(const Duration(milliseconds: 100));
              context.push('/help'); // Use GoRouter instead of Navigator.push
            },
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.all(8),
                  child: CustomSvgImage(imageName: 'Vector'),
                ),
                SizedBox(width: 4),
              ],
            ),
          ),
      ],
      elevation: 0,
    );
  }
}
