import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { EmpsItemResponseModel } from 'apps/ems-api/src/providers/models/emps/empsItemResponse.model';
import { WorkersItemResponseModel } from 'apps/ems-api/src/providers/models/worker/workersItemResponse.model';
import { UserModel as NMSUserModel } from 'apps/nms-api/src/providers/models/user/user.model';
import { ParentProfileModel } from 'apps/pms-api/src/providers/models/parent/parentProfile.model';
import { UsersModel as SSRUserModel } from 'apps/ssr-api/src/providers/models/user/users.model';
import { HttpService } from 'packages/http';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/userProfile.entity';
const basicAuth = `Basic ${Buffer.from(`${process.env.EMS_API_USER}:${process.env.EMS_API_PASSWORD}`).toString('base64')}`;
@Injectable()
export class SyncAccountsService {
  public readonly http: HttpService;
  constructor(
    http: HttpService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
  ) {
    this.http = http;
    //this.syncAccounts();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCron() {
    if (process.env.USER_SYNC_PROFILES === 'true') {
      console.log(
        'Called when the current second is ' + new Date().getSeconds(),
      );
      await this.syncAccounts();
    }
  }

  async syncEMSWorkerAccounts(isEmployee = false) {
    console.log('Syncing EMS accounts');
    let maxUsersExpected = 1000;
    let syncedUsers = 0;
    let page = 1;
    let allUsers: WorkersItemResponseModel[] = [];
    let allEmployees: EmpsItemResponseModel[] = [];
    // apply do while loop to get all users
    do {
      const offset = (page - 1) * 100;
      const {
        items: users,
        totalResults,
        count,
      } = await this.http.get<any>(
        isEmployee
          ? 'emps?totalResults=true&limit=100&expand=photo,assignments&offset=' +
              offset
          : 'workers?onlyData=true&totalResults=true&limit=100&offset=' +
              offset,
        null,
        false,
        {
          baseURL: process.env.EMS_API_URL,
          apiTenant: 'ems',
          headers: {
            Authorization: basicAuth,
          },
        },
      );
      if (isEmployee) {
        allEmployees = [...allEmployees, ...users];
      } else {
        allUsers = [...allUsers, ...users];
      }

      maxUsersExpected = totalResults;
      syncedUsers += count;
      page += 1;
      console.log('Syncing accounts');
    } while (syncedUsers < maxUsersExpected);

    //  console.log(allUsers);
    const existingUsers = await this.userRepository.find();
    const existingUserProfiles = await this.userProfileRepository.find();

    // for (const user of allUsers) {
    //   if (user.emailaddress) {
    //     console.log('valid user');
    //   }

    //   const emails = user.emails || [];
    //   const phones = user.phones || [];

    //   if (emails.length > 0 || phones.length > 0) {
    //     let existingUser = existingUsers.find(
    //       (u) =>
    //         emails.filter(
    //           (e) => e.EmailAddress?.toLowerCase() === u.email?.toLowerCase(),
    //         ).length > 0 ||
    //         phones.filter((p) => p.PhoneNumber === u.phone).length > 0,
    //     );
    //     let existingProfile = existingUserProfiles.find(
    //       (up) => up.PersonId === user.PersonId.toString() && up.Type === 'EMS',
    //     );

    //     if (!existingUser) {
    //       if (existingProfile) {
    //         existingUser = existingProfile.user;
    //       }
    //     }

    //     if (!existingUser) {
    //       existingUser = new User();
    //       existingUser.email = user.emails[0].EmailAddress?.toLowerCase();
    //       if (user.FullName) {
    //         existingUser.name = user.FullName;
    //       }

    //       if (user.DateOfBirth) {
    //         existingUser.DateOfBirth = user.DateOfBirth;
    //       }

    //       await this.userRepository.save(existingUser);
    //       console.log('User saved ' + existingUser.email);
    //     }

    //     if (!existingProfile) {
    //       existingProfile = new UserProfile();
    //       existingProfile.user = existingUser;
    //       existingProfile.PersonId = user.PersonId.toString();
    //       existingProfile.Type = 'EMS';
    //       existingProfile.email = user.emails[0].EmailAddress?.toLowerCase();
    //       existingProfile.WorkEmail =
    //         user.emails[0].EmailAddress?.toLowerCase();
    //       existingProfile.phone = user.phones[0].PhoneNumber;
    //       existingProfile.phones = JSON.stringify(user.phones);
    //       existingProfile.emails = JSON.stringify(user.emails);
    //       existingProfile.PersonNumber = user.PersonNumber;
    //       existingProfile.LastProfileUpdateDate = user.LastUpdateDate;
    //       await this.userProfileRepository.save(existingProfile);
    //       console.log('User profile saved ' + existingProfile.email);
    //     }
    //   } else {
    //     user.LastUpdatedBy = '';
    //     user.CreatedBy = '';
    //     if (JSON.stringify(user).indexOf('@') > -1) {
    //       console.log('email found');
    //       throw new Error(
    //         'No email found for user from SSR' + JSON.stringify(user),
    //       );
    //     } else {
    //       console.log(
    //         'No email found for user from EMS worker ' + JSON.stringify(user),
    //       );
    //     }
    //   }
    // }
    // let index = 0;
    for (const user of allEmployees) {
      // index++;
      // console.log(`Processing Erp user ${index} of ${allEmployees.length}`);
      if (
        user.UserName &&
        user.UserName?.indexOf('@') > -1 &&
        !user.WorkEmail
      ) {
        console.log('username found');
        user.WorkEmail = user.UserName;
      }

      if (user.UserName == 'MAl Masri') {
        console.log('username found');
      }

      user.WorkEmail = user.WorkEmail?.toLowerCase().trim();
      let phoneNumber = '';
      if (user.HomePhoneNumber || user.WorkPhoneNumber) {
        if (
          user.HomePhoneCountryCode &&
          user.HomePhoneNumber &&
          user.HomePhoneAreaCode
        ) {
          phoneNumber =
            user.HomePhoneCountryCode +
            user.HomePhoneAreaCode +
            user.HomePhoneNumber;
        }
        if (
          user.WorkPhoneCountryCode &&
          user.WorkPhoneNumber &&
          user.WorkPhoneAreaCode
        ) {
          phoneNumber =
            user.WorkPhoneCountryCode +
            user.WorkPhoneAreaCode +
            user.WorkPhoneNumber;
        }
      }

      phoneNumber = phoneNumber.replace(/[^0-9]/g, '');

      if (user.WorkEmail || phoneNumber) {
        let existingUser = existingUsers.find(
          (u) =>
            (user.WorkEmail === u.email && user.WorkEmail && u.email) ||
            (phoneNumber === u.phone && phoneNumber && u.phone),
        );
        if (!existingUser) {
          existingUser = existingUsers.find(
            (u) =>
              (user.WorkEmail &&
                u.emails &&
                u.emails.indexOf(user.WorkEmail) > -1) ||
              (phoneNumber && u.phones && u.phones.indexOf(phoneNumber) > -1),
          );
        }

        let existingProfile = existingUserProfiles.find(
          (up) => up.PersonId === user.PersonId.toString() && up.Type === 'EMS',
        );

        if (!existingUser) {
          if (existingProfile) {
            existingUser = existingProfile.user;
          }
        }

        const phoneList: string[] = [];
        if (existingUser && existingUser.phones) {
          phoneList.push(...existingUser.phones.split(','));
        }
        if (
          user.HomePhoneCountryCode &&
          user.HomePhoneNumber &&
          user.HomePhoneAreaCode
        ) {
          phoneList.push(
            user.HomePhoneCountryCode +
              user.HomePhoneAreaCode +
              user.HomePhoneNumber,
          );
        }

        if (
          user.WorkPhoneCountryCode &&
          user.WorkPhoneNumber &&
          user.WorkPhoneAreaCode
        ) {
          phoneList.push(
            user.WorkPhoneCountryCode +
              user.WorkPhoneAreaCode +
              user.WorkPhoneNumber,
          );
        }

        const emailsList: string[] = [];

        if (existingUser && existingUser.emails) {
          emailsList.push(...existingUser.emails.split(','));
        }
        if (user.WorkEmail) {
          emailsList.push(user.WorkEmail);
        }

        if (!existingUser) {
          existingUser = new User();
          existingUser.email = user.WorkEmail;

          if (user.DisplayName) {
            existingUser.name = user.DisplayName;
          } else if (user.FirstName && user.LastName) {
            existingUser.name =
              user.FirstName + ' ' + user.MiddleName + ' ' + user.LastName;
            existingUser.name = existingUser.name.replace('  ', ' ');
          }

          if (user.DateOfBirth) {
            existingUser.DateOfBirth = user.DateOfBirth;
          }

          if (user.Gender) {
            existingUser.Gender = user.Gender;
          }

          if (phoneNumber) {
            existingUser.phone = phoneNumber;
          }

          // if (!existingUser.email) {
          //   console.log(
          //     'No email found for user from EMS employee ' +
          //       JSON.stringify(user),
          //   );
          // }

          existingUser.phones = [...new Set(phoneList)].join(',');
          existingUser.emails = [...new Set(emailsList)].join(',');

          await this.userRepository.save(existingUser);
          console.log('User saved ' + existingUser.email);
        } else {
          // console.log('User already exists for ' + existingUser.email);
          if (user.WorkEmail == '<EMAIL>') {
            //existingUser.name = 'Adil Cardy';
            console.log('User name updated to Adil Cardy');
          }
          if (
            phoneNumber &&
            phoneNumber !== existingUser.phone &&
            !existingUser.phone
          ) {
            console.log(
              'User phone updated from ' +
                existingUser.phone +
                ' to ' +
                phoneNumber +
                ' because of ' +
                user.WorkEmail,
            );
            existingUser.phone = phoneNumber;
            await this.userRepository.save(existingUser);
            // console.log('User phone updated ' + existingUser.email);
          }

          if (
            user.WorkEmail &&
            user.WorkEmail !== existingUser.email &&
            !existingUser.email
          ) {
            console.log(
              'User email updated from ' +
                existingUser.email +
                ' to ' +
                user.WorkEmail +
                ' because of ' +
                phoneNumber,
            );
            existingUser.email = user.WorkEmail;
            await this.userRepository.save(existingUser);
            // console.log('User email updated ' + existingUser.email);
          }

          const joinedPhones = [...new Set(phoneList)].join(',');
          const joinedEmails = [...new Set(emailsList)].join(',');
          if (
            (joinedPhones && existingUser.phones !== joinedPhones) ||
            (joinedEmails && existingUser.emails !== joinedEmails)
          ) {
            console.log(
              'User phones or emails updated from ' +
                existingUser.phones +
                ' to ' +
                joinedPhones +
                ' and ' +
                existingUser.emails +
                ' to ' +
                joinedEmails,
            );
            existingUser.phones = joinedPhones;
            existingUser.emails = joinedEmails;
            await this.userRepository.save(existingUser);
          }
        }

        let employeeUniqueId = '';
        if (user.links && user.links.length > 0) {
          const empsLink = user.links.find((link) => link.rel === 'self');
          if (empsLink) {
            employeeUniqueId = empsLink.href.split('/').pop() || '';
          }
        }

        let assignmentUniqueId = '';
        if (
          user.assignments &&
          user.assignments.length > 0 &&
          user.assignments[0].links &&
          user.assignments[0].links.length > 0
        ) {
          const assignmentLink = user.assignments[0].links.find(
            (link) => link.rel === 'lov' && link.name === 'GradeIdLOV',
          );
          if (assignmentLink) {
            assignmentUniqueId = assignmentLink.href
              .split('/assignments/')[1]
              .split('/')[0];
          }
        }

        let imageId: string | null = null;
        if (user.photo && user.photo.length > 0) {
          imageId = user.photo[0].ImageId.toString();
        }

        if (!existingProfile) {
          existingProfile = new UserProfile();
          existingProfile.user = existingUser;
          existingProfile.PersonId = user.PersonId.toString();
          existingProfile.Type = 'EMS';
          existingProfile.email = user.WorkEmail;
          existingProfile.WorkEmail = user.WorkEmail;
          existingProfile.UniqueId = employeeUniqueId;
          const phones: any[] = [];
          if (user.HomePhoneCountryCode && user.HomePhoneNumber) {
            existingProfile.phone =
              user.HomePhoneCountryCode + user.HomePhoneNumber;
            if (user.HomePhoneAreaCode) {
              existingProfile.phone =
                user.HomePhoneCountryCode +
                user.HomePhoneAreaCode +
                user.HomePhoneNumber;
            }
            phones.push({
              PhoneNumber: existingProfile.WorkEmail,
              PhoneType: 'Home',
            });
          }
          if (user.WorkPhoneCountryCode && user.WorkPhoneNumber) {
            existingProfile.phone =
              user.WorkPhoneCountryCode + user.WorkPhoneNumber;
            if (user.WorkPhoneAreaCode) {
              existingProfile.phone =
                user.WorkPhoneCountryCode +
                user.WorkPhoneAreaCode +
                user.WorkPhoneNumber;
            }
            phones.push({
              PhoneNumber: existingProfile.WorkEmail,
              PhoneType: 'Work',
            });
          }

          if (user.UserName) {
            existingProfile.UserName = user.UserName;
          }

          existingProfile.phones = JSON.stringify(phones);

          existingProfile.PersonNumber = user.PersonNumber;
          if (imageId != null) {
            existingProfile.FusionImageId = imageId;
          }

          existingProfile.AssignmentUniqueId = assignmentUniqueId;
          existingProfile.LastProfileUpdateDate = user.LastUpdateDate;
          await this.userProfileRepository.save(existingProfile);
          console.log('User profile saved ' + existingProfile.email);
        } else {
          switch (user.WorkEmail) {
            case '<EMAIL>': {
              // existingProfile.UserName = 'MAl Masri';
              break;
            }
            case '<EMAIL>': {
              // existingProfile.UserName = 'BJose';// already exists
              break;
            }

            case '<EMAIL>': {
              // existingProfile.UserName = 'AShuhail';// already exists
              break;
            }

            case '<EMAIL>': {
              //    existingProfile.UserName = 'erp.integration';// already abdul vahal
              break;
            }
          }

          if (
            existingProfile.UniqueId !== employeeUniqueId &&
            employeeUniqueId
          ) {
            existingProfile.UniqueId = employeeUniqueId;
            await this.userProfileRepository.save(existingProfile);
          }

          if (
            existingProfile.AssignmentUniqueId !== assignmentUniqueId &&
            assignmentUniqueId
          ) {
            existingProfile.AssignmentUniqueId = assignmentUniqueId;
            await this.userProfileRepository.save(existingProfile);
          }

          if (
            existingProfile.FusionImageId !== imageId &&
            imageId &&
            imageId != null
          ) {
            existingProfile.FusionImageId = imageId;
            await this.userProfileRepository.save(existingProfile);
          }

          if (existingProfile.UserName !== user.UserName && user.UserName) {
            existingProfile.UserName = user.UserName;
            await this.userProfileRepository.save(existingProfile);
            console.log(
              'User profile UserName updated ' + existingProfile.email,
            );
          }
        }
      } else {
        // if (JSON.stringify(user).indexOf('@') > -1) {
        //   console.log('email found');
        //   throw new Error(
        //     'No email found for user from employee' + JSON.stringify(user),
        //   );
        // }
        // console.log(
        //   'No email found for user from EMS employee ' + JSON.stringify(user),
        // );
      }
    }
  }

  async syncEMSempsAccounts() {
    console.log('Syncing EMS accounts');
    await this.syncEMSWorkerAccounts(true);
  }

  async syncSSRAccounts() {
    let maxUsersExpected = 1000;
    let syncedUsers = 0;
    let allUsers: SSRUserModel[] = [];
    const url = process.env.HALO_ITMS_AUTH_URL;
    const clientId = process.env.HALO_ITMS_CLIENT_ID;
    const clientSecret = process.env.HALO_ITMS_CLIENT_SECRET;

    // apply do while loop to get all users
    do {
      const { users, record_count } = await this.http.get<any>(
        'Users',
        {
          pageinate: true,
          page_size: 100000,
          page_no: 1,
        },
        false,
        {
          baseURL: process.env.SSR_API_URL,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          postHeaders: {
            'Content-Type': 'application/json',
          },
          authTokenUrl: url + '/auth/token',
          authTokenResponseKey: 'access_token',
          apiTenant: 'ssr',
          authTokenParams: {
            grant_type: 'client_credentials',
            scope: 'all',
            client_id: clientId,
            client_secret: clientSecret,
          },
        },
      );
      allUsers = [...allUsers, ...users];
      maxUsersExpected = allUsers.length;
      syncedUsers += record_count;
    } while (syncedUsers < maxUsersExpected);

    console.log('Syncing SSR accounts');

    const existingUsers = await this.userRepository.find();
    const existingUserProfiles = await this.userProfileRepository.find();

    // let index = 0;
    for (const user of allUsers) {
      //index++;
      //console.log(`Processing SSR user ${index} of ${allUsers.length}`);
      const isValidUser = user.emailaddress == user.activedirectory_dn;
      const isGeneralUser = user.name == user.firstname;
      const nonAdUser =
        user.emailaddress &&
        user.emailaddress.indexOf('@') > -1 &&
        !isValidUser;
      const isUserName = user.UserName?.indexOf('@') > -1;
      if (isUserName) {
        console.log('username found');
      }

      const loginUser =
        user.activedirectory_dn == user.login &&
        user.login &&
        user.login?.indexOf('@') > -1;

      if (isGeneralUser) {
        //  console.log('valid user');
      } else {
        //  console.log('invalid user');
      }
      let email = isValidUser
        ? user.activedirectory_dn
        : nonAdUser
          ? user.emailaddress
          : loginUser
            ? user.login
            : isUserName
              ? user.UserName
              : user.name;

      email = email?.toLowerCase().trim();

      if (user.name && user.name.indexOf('<EMAIL>') > -1) {
        console.log('email found');
      }
      if (!user.id) {
        console.log('No id found for user from SSR' + JSON.stringify(user));
        throw new Error('No id found for user from SSR' + JSON.stringify(user));
      }

      let phoneNumber = '';
      if (
        (user.phonenumber || user.phonenumber_preferred) &&
        user.phonenumber !== 'Unknown' &&
        user.phonenumber_preferred !== 'Unknown'
      ) {
        phoneNumber = user.phonenumber_preferred || user.phonenumber || '';
        phoneNumber = phoneNumber.replace(/[^0-9]/g, '');
      }
      if ((email && email.indexOf('@') > -1) || phoneNumber) {
        let existingUser = existingUsers.find(
          (u) =>
            (u.email && u.email === email) ||
            (u.phone && u.phone === phoneNumber),
        );
        if (!existingUser) {
          existingUser = existingUsers.find(
            (u) =>
              (email && u.emails && u.emails.indexOf(email) > -1) ||
              (phoneNumber && u.phones && u.phones.indexOf(phoneNumber) > -1),
          );
        }
        let existingProfile = existingUserProfiles.find(
          (up) =>
            up.PersonId === user.id.toString() &&
            up.Type ===
              'SSR' +
                (isGeneralUser
                  ? 'General'
                  : isValidUser
                    ? 'AD'
                    : loginUser
                      ? 'ADLogin'
                      : isUserName
                        ? 'UN'
                        : nonAdUser
                          ? 'NonAD'
                          : ''),
        );

        if (!existingUser) {
          if (existingProfile) {
            existingUser = existingProfile.user;
          }
        }

        const phoneList: string[] = [];
        if (existingUser && existingUser.phones) {
          phoneList.push(...existingUser.phones.split(','));
        }
        if (user.phonenumber) {
          phoneList.push(user.phonenumber.replace(/[^0-9]/g, ''));
        }

        if (user.phonenumber_preferred) {
          phoneList.push(user.phonenumber_preferred.replace(/[^0-9]/g, ''));
        }

        const emailsList: string[] = [];

        if (existingUser && existingUser.emails) {
          emailsList.push(...existingUser.emails.split(','));
        }

        if (user.emailaddress) {
          emailsList.push(user.emailaddress);
        }

        if (!existingUser) {
          existingUser = new User();
          if (email) {
            existingUser.email = email;
          }
          if (user.name) {
            existingUser.name = user.name;
          }

          if (phoneNumber) {
            existingUser.phone = phoneNumber;
          }

          existingUser.phones = [...new Set(phoneList)].join(',');
          existingUser.emails = [...new Set(emailsList)].join(',');

          if (existingUser.email || existingUser.phone) {
            await this.userRepository.save(existingUser);
          }
          console.log('User saved ' + existingUser.email);
        } else {
          //console.log('User already exists for ' + existingUser.email);

          if (email && email !== existingUser.email && !existingUser.email) {
            console.log(
              'User email updated from ' +
                existingUser.email +
                ' to ' +
                email +
                ' because of ' +
                phoneNumber,
            );
            existingUser.email = email;
            await this.userRepository.save(existingUser);
            // console.log('User email updated ' + existingUser.email);
          }

          if (
            phoneNumber &&
            phoneNumber !== existingUser.phone &&
            !existingUser.phone
          ) {
            console.log(
              'User phone updated from ' +
                existingUser.phone +
                ' to ' +
                phoneNumber +
                ' because of ' +
                email,
            );
            existingUser.phone = phoneNumber;
            await this.userRepository.save(existingUser);
            //console.log('User phone updated ' + existingUser.phone);
          }

          const joinedPhones = [...new Set(phoneList)].join(',');
          const joinedEmails = [...new Set(emailsList)].join(',');
          if (
            (joinedPhones && existingUser.phones !== joinedPhones) ||
            (joinedEmails && existingUser.emails !== joinedEmails)
          ) {
            console.log(
              'User phones or emails updated from ' +
                existingUser.phones +
                ' to ' +
                joinedPhones +
                ' and ' +
                existingUser.emails +
                ' to ' +
                joinedEmails,
            );
            existingUser.phones = joinedPhones;
            existingUser.emails = joinedEmails;
            await this.userRepository.save(existingUser);
          }
        }

        if (!existingProfile) {
          existingProfile = new UserProfile();
          existingProfile.user = existingUser;
          existingProfile.PersonId = user.id.toString();
          existingProfile.Type =
            'SSR' +
            (isGeneralUser
              ? 'General'
              : isValidUser
                ? 'AD'
                : loginUser
                  ? 'ADLogin'
                  : nonAdUser
                    ? 'NonAD'
                    : '');
          if (email) {
            existingProfile.email = email;
          }

          if (user.phonenumber) {
            existingProfile.phone = user.phonenumber.replace(/[^0-9]/g, '');
          }

          if (user.phonenumber_preferred) {
            existingProfile.phone = user.phonenumber_preferred.replace(
              /[^0-9]/g,
              '',
            );
          }

          if (user.client_name) {
            existingProfile.WorkerType = user.client_name;
          }

          await this.userProfileRepository.save(existingProfile);
          console.log('User profile saved ' + existingProfile.email);
        }
      } else {
        if (JSON.stringify(user).indexOf('@') > -1) {
          console.log(
            'No email found for user from SSR' + JSON.stringify(user),
          );
          // throw new Error(
          //   'No email found for user from SSR' + JSON.stringify(user),
          // );
        }
        //console.log('No email found for user from SSR' + JSON.stringify(user));
      }
    }
  }

  public async syncPMSAccounts() {
    console.log('Syncing PMS accounts');
    let maxUsersExpected = 1000;
    let syncedUsers = 0;
    let allUsers: ParentProfileModel[] = [];
    // apply do while loop to get all users
    const url = process.env.PMS_API_URL;
    const userName = process.env.PMS_API_CLIENT_ID;
    const password = process.env.PMS_API_CLIENT_SECRET;
    const basicAuthenticationToken = Buffer.from(
      `${userName}:${password}`,
    ).toString('base64');
    const defaultTestingParentId = process.env.PMS_API_PARENT_ID;
    do {
      const users = await this.http.get<ParentProfileModel[]>(
        'parent/getAllParents',
        {
          pageNumber: 1,
          pageSize: 100000,
        },
        true,
        {
          baseURL: url,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${basicAuthenticationToken}`,
          },
          postHeaders: {
            'Content-Type': 'application/json',
          },
          authTokenUrl: url + '/authenticate/AuthenticateParentViaIdentifier',
          authTokenResponseKey: 'JwtToken',
          authTokenContentType: 'application/json',
          apiTenant: 'pms',
          authTokenParams: {
            userId: defaultTestingParentId,
            // Password: password,
          },
        },
      );
      allUsers = [...allUsers, ...users];

      const notVerifiedParents = await this.http.get<ParentProfileModel[]>(
        'parent/GetAllNotVeryfiedParents',
        {
          pageNumber: 1,
          pageSize: 100000,
        },
        true,
        {
          baseURL: url,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${basicAuthenticationToken}`,
          },
          postHeaders: {
            'Content-Type': 'application/json',
          },
          authTokenUrl: url + '/authenticate/AuthenticateParentViaIdentifier',
          authTokenResponseKey: 'JwtToken',
          authTokenContentType: 'application/json',
          apiTenant: 'pms',
          authTokenParams: {
            userId: defaultTestingParentId,
            // Password: password,
          },
        },
      );
      console.log(`Found ${notVerifiedParents.length} not verified parents`);
      allUsers = [...allUsers, ...notVerifiedParents];
      maxUsersExpected = allUsers.length;
      syncedUsers += users.length;
    } while (syncedUsers < maxUsersExpected);

    // console.log(allUsers);
    const existingUsers = await this.userRepository.find();
    const existingUserProfiles = await this.userProfileRepository.find();
    for (const user of allUsers) {
      const parentProfile: ParentProfileModel =
        user.ParentProfiles?.[0] ?? new ParentProfileModel();
      let phoneNumber = '';
      if (user.PhoneNumber) {
        phoneNumber = user.PhoneNumber.replace(/[^0-9]/g, '');
      }

      const email = user.Email?.toLowerCase().trim();
      if (email == '<EMAIL>') {
        //  console.log('email found');
      }

      if (email || phoneNumber) {
        //  console.log('valid user');

        if ((email && email.indexOf('@') > -1) || phoneNumber) {
          let existingUser = existingUsers.find(
            (u) =>
              (u.email && u.email === email) ||
              (u.phone && u.phone === phoneNumber),
          );
          if (!existingUser) {
            existingUser = existingUsers.find(
              (u) =>
                (email && u.emails && u.emails.indexOf(email) > -1) ||
                (phoneNumber && u.phones && u.phones.indexOf(phoneNumber) > -1),
            );
          } else {
            // console.log(
            //   'User already exists for ' +
            //     existingUser.email +
            //     ' ' +
            //     existingUser.phone,
            // );
          }
          let existingProfile = existingUserProfiles.find(
            (up) => up.PersonId === user.Id.toString() && up.Type === 'PMS',
          );
          //545123456
          if (phoneNumber.indexOf('545123456') > -1) {
            console.log('phone number found');
          }
          if (user.Id.toString().length < 5) {
            console.log('issue with user id ' + user.Id.toString());
            throw new Error(
              'issue with user id ' +
                user.Id.toString() +
                ' ' +
                JSON.stringify(user),
            );
          }
          if (existingProfile && existingProfile.PersonId == '5725') {
            console.log('profile found');
          }

          if (!existingUser) {
            if (existingProfile) {
              existingUser = existingProfile.user;
            }
          }

          const phoneList: string[] = [];
          if (existingUser && existingUser.phones) {
            phoneList.push(...existingUser.phones.split(','));
          }
          if (user.PhoneNumber) {
            phoneList.push(user.PhoneNumber.replace(/[^0-9]/g, ''));
          }

          const emailsList: string[] = [];

          if (existingUser && existingUser.emails) {
            emailsList.push(...existingUser.emails.split(','));
          }

          if (user.Email) {
            emailsList.push(user.Email.toLowerCase().trim());
          }

          if (!existingUser) {
            existingUser = new User();
            if (email) {
              existingUser.email = email;
            }

            if (parentProfile.fullNameInEnglish) {
              existingUser.name = parentProfile.fullNameInEnglish;
            }

            if (phoneNumber) {
              existingUser.phone = phoneNumber;
            }

            existingUser.phones = [...new Set(phoneList)].join(',');
            existingUser.emails = [...new Set(emailsList)].join(',');

            if (existingUser.email || existingUser.phone) {
              await this.userRepository.save(existingUser);
            }
            console.log(
              'User saved ' + existingUser.email + ' ' + existingUser.phone,
            );
          } else {
            //console.log('User already exists for ' + existingUser.email);

            if (email && email !== existingUser.email && !existingUser.email) {
              console.log(
                'User email updated from ' +
                  existingUser.email +
                  ' to ' +
                  email +
                  ' because of ' +
                  phoneNumber,
              );
              existingUser.email = email;
              await this.userRepository.save(existingUser);
              // console.log('User email updated ' + existingUser.email);
            }

            if (
              phoneNumber &&
              phoneNumber !== existingUser.phone &&
              !existingUser.phone
            ) {
              console.log(
                'User phone updated from ' +
                  existingUser.phone +
                  ' to ' +
                  phoneNumber +
                  ' because of ' +
                  email,
              );
              existingUser.phone = phoneNumber;
              await this.userRepository.save(existingUser);
              //console.log('User phone updated ' + existingUser.phone);
            }

            const joinedPhones = [...new Set(phoneList)].join(',');
            const joinedEmails = [...new Set(emailsList)].join(',');
            if (
              (joinedPhones && existingUser.phones !== joinedPhones) ||
              (joinedEmails && existingUser.emails !== joinedEmails)
            ) {
              console.log(
                'User phones or emails updated from ' +
                  existingUser.phones +
                  ' to ' +
                  joinedPhones +
                  ' and ' +
                  existingUser.emails +
                  ' to ' +
                  joinedEmails,
              );
              existingUser.phones = joinedPhones;
              existingUser.emails = joinedEmails;
              await this.userRepository.save(existingUser);
            }
          }

          if (!existingProfile) {
            existingProfile = new UserProfile();
            existingProfile.user = existingUser;
            existingProfile.PersonId = user.Id.toString();
            existingProfile.Type = 'PMS';
            if (email) {
              existingProfile.email = email;
            }

            if (phoneNumber) {
              existingProfile.phone = phoneNumber;
            }

            await this.userProfileRepository.save(existingProfile);
            console.log('User profile saved ' + existingProfile.email);
          } else {
            if (phoneNumber && phoneNumber != existingProfile.phone) {
              console.log(
                'User phone updated from ' +
                  existingProfile.phone +
                  ' to ' +
                  phoneNumber +
                  ' because of ' +
                  email,
              );
              existingProfile.phone = phoneNumber;
              await this.userProfileRepository.save(existingProfile);
            }

            if (user.Id && user.Id.toString() != existingProfile.PersonId) {
              console.log(
                'User PersonId updated from ' +
                  existingProfile.PersonId +
                  ' to ' +
                  user.Id.toString(),
              );
              existingProfile.PersonId = user.Id.toString();
              await this.userProfileRepository.save(existingProfile);
            }
          }
        } else {
          // if (JSON.stringify(user).indexOf('@') > -1) {
          //   console.log(
          //     'No email found for user from SSR' + JSON.stringify(user),
          //   );
          //   // throw new Error(
          //   //   'No email found for user from SSR' + JSON.stringify(user),
          //   // );
          // }
          //console.log('No email found for user from SSR' + JSON.stringify(user));
        }
      }
    }

    // const existingUsers = await this.userRepository.find();
    // const existingUserProfiles = await this.userProfileRepository.find();

    // for (const user of allUsers) {
    //   let existingUser = existingUsers.find((u) => u.email === user.email);
    //   let existingProfile = existingUserProfiles.find(
    //     (up) => up.PersonId === user.id && up.Type === 'PMS',
    //   );

    //   if (!existingUser) {
    //     if (existingProfile) {
    //       existingUser = existingProfile.user;
    //     }
    //   }

    //   if (!existingUser) {
    //     existingUser = new User();
    //     if (user.email) {
    //       existingUser.email = user.email;
    //     }
    //     if (user.name) {
    //       existingUser.name = user.name;
    //     }

    //     if (existingUser.name && existingUser.email) {
    //       await this.userRepository.save(existingUser);
    //     }
    //     console.log('User saved ' + existingUser.email);
    //   }

    //   if (!existingProfile) {
    //     existingProfile = new UserProfile();
    //     existingProfile.user = existingUser;
    //     existingProfile.PersonId = user.id;
    //     existingProfile.Type = 'PMS';
    //     if (user.email) {
    //       existingProfile.email = user.email;
    //     }
    //     if (user.phone) {
    //       existingProfile.phone = user.phone;
    //     }

    //     await this.userProfileRepository.save(existingProfile);
    //     console.log('User profile saved ' + existingProfile.email);
    //   }
    // }
  }

  async syncNMSAccounts() {
    console.log('Syncing NMS accounts');

    let maxUsersExpected = 1000;
    let syncedUsers = 0;
    let allUsers: NMSUserModel[] = [];
    // apply do while loop to get all users

    const url = process.env.PMS_API_URL;
    const userName = process.env.PMS_API_CLIENT_ID;
    const password = process.env.PMS_API_CLIENT_SECRET;
    const basicAuthenticationToken = Buffer.from(
      `${userName}:${password}`,
    ).toString('base64');
    const defaultTestingParentId = process.env.PMS_API_PARENT_ID;

    do {
      const users = await this.http.get<NMSUserModel[]>(
        'user/getAllUsers',
        {
          pageNumber: 1,
          pageSize: 100000,
        },
        true,
        {
          baseURL: url,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${basicAuthenticationToken}`,
          },
          postHeaders: {
            'Content-Type': 'application/json',
          },
          authTokenUrl: url + '/authenticate/AuthenticateParentViaIdentifier',
          authTokenResponseKey: 'JwtToken',
          authTokenContentType: 'application/json',
          apiTenant: 'pms',
          authTokenParams: {
            userId: defaultTestingParentId,
            // Password: password,
          },
        },
      );
      allUsers = [...allUsers, ...users];
      maxUsersExpected = allUsers.length;
      syncedUsers += users.length;
    } while (syncedUsers < maxUsersExpected);

    console.log(allUsers);
    const existingUsers = await this.userRepository.find();
    const existingUserProfiles = await this.userProfileRepository.find();

    for (const user of allUsers) {
      if (user.email) {
        let existingUser = existingUsers.find((u) => u.email === user.email);
        let existingProfile = existingUserProfiles.find(
          (up) => up.PersonId === user.id.toString() && up.Type === 'NMS',
        );

        if (!existingUser) {
          if (existingProfile) {
            existingUser = existingProfile.user;
          }
        }

        if (!existingUser) {
          existingUser = new User();
          if (user.email) {
            existingUser.email = user.email;
          }
          if (user.firstName && user.lastName) {
            existingUser.name =
              user.firstName + ' ' + user.middleName + ' ' + user.lastName;
            existingUser.name = existingUser.name.replace('  ', ' ');
          }

          if (existingUser.name && existingUser.email) {
            await this.userRepository.save(existingUser);
          }
          console.log('User saved ' + existingUser.email);
        }

        if (!existingProfile) {
          existingProfile = new UserProfile();
          existingProfile.user = existingUser;
          existingProfile.PersonId = user.id.toString();
          existingProfile.Type = 'NMS';
          if (user.email) {
            existingProfile.email = user.email;
          }
          if (user.phoneNumber) {
            existingProfile.phone = user.phoneNumber.replace(/[^0-9]/g, '');
          }

          if (user.lastUpdatedDate) {
            existingProfile.LastProfileUpdateDate = user.lastUpdatedDate;
          }
          await this.userProfileRepository.save(existingProfile);
          console.log('User profile saved ' + existingProfile.email);
        }
      } else {
        console.log('No email found for user');
      }
    }
  }

  async syncAccounts() {
    console.log('Syncing accounts for EMS workers');
    await this.syncEMSWorkerAccounts();
    console.log('Syncing accounts for EMS employees');
    await this.syncEMSempsAccounts();
    console.log('Syncing accounts for SSR');
    await this.syncSSRAccounts();
    console.log('Syncing accounts for PMS');
    await this.syncPMSAccounts();
    //  await this.syncNMSAccounts();
  }
}
