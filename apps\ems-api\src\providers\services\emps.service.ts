import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { EmpsModel } from '../models/emps/emps.model';
import { EmpsAssignmentsAssignmentDFFItemPostRequestModel } from '../models/emps/empsAssignmentsAssignmentDFFItemPostRequest.model';
import { EmpsAssignmentsAssignmentDFFItemResponseModel } from '../models/emps/empsAssignmentsAssignmentDFFItemResponse.model';
import { EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel } from '../models/emps/empsAssignmentsAssignmentExtraInformationItemPostRequest.model';
import { EmpsAssignmentsAssignmentExtraInformationItemResponseModel } from '../models/emps/empsAssignmentsAssignmentExtraInformationItemResponse.model';
import { EmpsAssignmentsEmprepsItemResponseModel } from '../models/emps/empsAssignmentsEmprepsItemResponse.model';
import { EmpsAssignmentsItemPostRequestModel } from '../models/emps/empsAssignmentsItemPostRequest.model';
import { EmpsAssignmentsItemResponseModel } from '../models/emps/empsAssignmentsItemResponse.model';
import { EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel } from '../models/emps/empsAssignmentsPeopleGroupKeyFlexfieldItemResponse.model';
import { EmpsDirectReportsItemResponseModel } from '../models/emps/empsDirectReportsItemResponse.model';
import { EmpsEmployerResponseModel } from '../models/emps/empsEmployee.model';
import { GradeIdLOVItemModel } from '../models/emps/empsGradeLOV.model';
import { EmpsItemPostRequestModel } from '../models/emps/empsItemPostRequest.model';
import { EmpsItemResponseModel } from '../models/emps/empsItemResponse.model';
import { EmpsPersonDFFItemPostRequestModel } from '../models/emps/empsPersonDFFItemPostRequest.model';
import { EmpsPersonDFFItemResponseModel } from '../models/emps/empsPersonDFFItemResponse.model';
import { EmpsPersonExtraInformationItemPostRequestModel } from '../models/emps/empsPersonExtraInformationItemPostRequest.model';
import { EmpsPersonExtraInformationItemResponseModel } from '../models/emps/empsPersonExtraInformationItemResponse.model';
import { EmpsPhotoItemPostRequestModel } from '../models/emps/empsPhotoItemPostRequest.model';
import { EmpsPhotoItemResponseModel } from '../models/emps/empsPhotoItemResponse.model';
import { EmpsRolesItemPostRequestModel } from '../models/emps/empsRolesItemPostRequest.model';
import { EmpsRolesItemResponseModel } from '../models/emps/empsRolesItemResponse.model';
import { EmpsVisasItemResponseModel } from '../models/emps/empsVisasItemResponse.model';
@Injectable()
export class EmpsService {
  constructor(private readonly http: HttpService) {}

  public async getEmpsAssignmentsExtraInfo(
    empsUniqID: string,
    assignmentsUniqID: string,
    assignmentExtraInformationUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsAssignmentsAssignmentExtraInformationItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<EmpsAssignmentsAssignmentExtraInformationItemResponseModel>(
        `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/child/assignmentExtraInformation/${assignmentExtraInformationUniqID}`,
        {
          empsUniqID: empsUniqID,
          assignmentsUniqID: assignmentsUniqID,
          assignmentExtraInformationUniqID: assignmentExtraInformationUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
          'Effective-Of': effectiveOf,
        },
        false,
      );
    return response;
  }

  public async postEmpsRoles(
    model: EmpsRolesItemPostRequestModel,
  ): Promise<EmpsRolesItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<EmpsRolesItemPostRequestModel>(
      `emps/{empsUniqID}/child/roles`,
      model,
    );
    return response;
  }

  public async getEmpsPhoto(
    empsUniqID: string,
    imageId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<EmpsPhotoItemResponseModel> {
    const url = `emps/${empsUniqID}/child/photo/${imageId}`;
    const response = await this.http.get<EmpsPhotoItemResponseModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

   public async getEmpsAssignmentDFF(
    empsUniqID: string,
    assignmentsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<GradeIdLOVItemModel> {
     const url = `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/child/assignmentDFF`;

    const response = await this.http.get<GradeIdLOVItemModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }
//  const url = `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/child/assignmentDFF`;
  // Grade
  // public async getLovGrade(
  //   empsUniqID: string,
  //   assignmentsUniqID: string,
  //   expand?: string,
  //   fields?: string,
  //   onlyData?: boolean,
  //   dependency?: string,
  //   links?: string,
  // ): Promise<GradeIdLOVItemModel> {
  //   // -- not used
  //   const url = `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/lov/GradeIdLOV`;
  //   const response = await this.http.get<GradeIdLOVItemModel>(
  //     url,
  //     {
  //       expand: expand,
  //       fields: fields,
  //       onlyData: onlyData,
  //       dependency: dependency,
  //       links: links,
  //     },
  //     false,
  //   );
  //   return response;
  // }
  public async getLovGrade(
    empsUniqID: string,
    assignmentsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<GradeIdLOVItemModel> {
    const url = `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/lov/GradeIdLOV`;

    const response = await this.http.get<GradeIdLOVItemModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }
  public async postEmpsPersonDFF(
    model: EmpsPersonDFFItemPostRequestModel,
  ): Promise<EmpsPersonDFFItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<EmpsPersonDFFItemPostRequestModel>(
      `emps/{empsUniqID}/child/personDFF`,
      model,
    );
    return response;
  }

  public async getEmpsPersonExtraInfo(
    empsUniqID: string,
    personExtraInformationUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<EmpsPersonExtraInformationItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<EmpsPersonExtraInformationItemResponseModel>(
        `emps/${empsUniqID}/child/personExtraInformation/${personExtraInformationUniqID}`,
        {
          empsUniqID: empsUniqID,
          personExtraInformationUniqID: personExtraInformationUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getEmpsAssignments(
    empsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsAssignmentsItemResponseModel> {
    // Construct the API URL using the empsUniqID parameter
    const url = `emps/${empsUniqID}/child/assignments`;

    const response = await this.http.get<EmpsAssignmentsItemResponseModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }
  public async getEmployerLOV(
    empsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsEmployerResponseModel> {
    // Construct the API URL using the empsUniqID parameter
    const url = `emps/${empsUniqID}/lov/LegalEmployerLOV`;

    const response = await this.http.get<EmpsEmployerResponseModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }
  public async getEmps_2(
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    orderBy?: string,
    finder?: string,
    effectiveDate?: string,
    effectiveOf?: string,
  ): Promise<EmpsModel[]> {
    // -- not used
    const response = await this.http.get<EmpsModel[]>(
      `emps`,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        orderBy: orderBy,
        finder: finder,
        effectiveDate: effectiveDate,
        'Effective-Of': effectiveOf,
      },
      true,
    );
    return response;
  }

  public async postEmps_2(
    model: EmpsItemPostRequestModel,
  ): Promise<EmpsItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<EmpsItemPostRequestModel>(
      `emps`,
      model,
    );
    return response;
  }

  public async postEmpsAssignmentsExtraInfoList(
    model: EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel,
  ): Promise<EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel>(
        `emps/{empsUniqID}/child/assignments/{assignmentsUniqID}/child/assignmentExtraInformation`,
        model,
      );
    return response;
  }

  public async getEmpsAssignmentsPeopleGroupKFById(
    empsUniqID: string,
    assignmentsUniqID: string,
    _PEOPLE_GROUP_ID: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel>(
        `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/child/peopleGroupKeyFlexfield/${_PEOPLE_GROUP_ID}`,
        {
          empsUniqID: empsUniqID,
          assignmentsUniqID: assignmentsUniqID,
          _PEOPLE_GROUP_ID: _PEOPLE_GROUP_ID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async postEmpsPersonExtraInfoList(
    model: EmpsPersonExtraInformationItemPostRequestModel,
  ): Promise<EmpsPersonExtraInformationItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<EmpsPersonExtraInformationItemPostRequestModel>(
        `emps/{empsUniqID}/child/personExtraInformation`,
        model,
      );
    return response;
  }

  public async getEmpsPersonDFFById(
    empsUniqID: string,
    personId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<EmpsPersonDFFItemResponseModel> {
    // -- not used
    const response = await this.http.get<EmpsPersonDFFItemResponseModel>(
      `emps/${empsUniqID}/child/personDFF/${personId}`,
      {
        empsUniqID: empsUniqID,
        PersonId: personId,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

  public async getEmpsRolesById(
    empsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsRolesItemResponseModel> {
    // Construct the API URL to match the provided endpoint
    const url = `emps/${empsUniqID}/child/roles`; // Make the API call with the required parameters
    const response = await this.http.get<EmpsRolesItemResponseModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }

  public async getEmpsVisas(
    empsUniqID: string,
    visasUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsVisasItemResponseModel> {
    // -- not used
    const response = await this.http.get<EmpsVisasItemResponseModel>(
      `emps/${empsUniqID}/child/visas/${visasUniqID}`,
      {
        empsUniqID: empsUniqID,
        visasUniqID: visasUniqID,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }

  public async getEmpsAssignmentsEmpRepsById(
    empsUniqID: string,
    assignmentsUniqID: string,
    emprepsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsAssignmentsEmprepsItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<EmpsAssignmentsEmprepsItemResponseModel>(
        `emps/${empsUniqID}/child/assignments/${assignmentsUniqID}/child/empreps/${emprepsUniqID}`,
        {
          empsUniqID: empsUniqID,
          assignmentsUniqID: assignmentsUniqID,
          emprepsUniqID: emprepsUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
          'Effective-Of': effectiveOf,
        },
        false,
      );
    return response;
  }

  public async postEmpsAssignmentsDFF(
    model: EmpsAssignmentsAssignmentDFFItemPostRequestModel,
  ): Promise<EmpsAssignmentsAssignmentDFFItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<EmpsAssignmentsAssignmentDFFItemPostRequestModel>(
        `emps/{empsUniqID}/child/assignments/{assignmentsUniqID}/child/assignmentDFF`,
        model,
      );
    return response;
  }

  public async postEmpsAssignmentsList(
    model: EmpsAssignmentsItemPostRequestModel,
  ): Promise<EmpsAssignmentsItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<EmpsAssignmentsItemPostRequestModel>(
      `emps/{empsUniqID}/child/assignments`,
      model,
    );
    return response;
  }

  public async getEmpsDirectReportsById(
    empsUniqID: string,
    directReportsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<EmpsDirectReportsItemResponseModel> {
    // -- not used
    const response = await this.http.get<EmpsDirectReportsItemResponseModel>(
      `emps/${empsUniqID}/child/directReports/${directReportsUniqID}`,
      {
        empsUniqID: empsUniqID,
        directReportsUniqID: directReportsUniqID,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

  // public async getEmpsPhotoImage(
  //   empsUniqID: string,
  //   imageId: number,
  // ): Promise<ArrayBuffer> {
  //   const response = await this.http.get<ArrayBuffer>(
  //     `emps/${empsUniqID}/child/photo/${imageId}/enclosure/Image`,
  //     {},
  //     false,
  //     {
  //       responseType: 'arraybuffer',
  //       // headers: {
  //       //   Accept: 'application/octet-stream',
  //       //   'Content-Type': 'application/octet-stream',
  //       // },
  //     },
  //   );
  //   return response;
  // }
  public async getEmpsPhotoImage(
  empsUniqID: string,
  imageId: number,
): Promise<ArrayBuffer> {
  // Construct the API URL using the empsUniqID and imageId parameters
  const url = `emps/${empsUniqID}/child/photo/${imageId}/enclosure/Image`;
  
  const response = await this.http.get<ArrayBuffer>(
    url,
    {},
    false,
    {
      responseType: 'arraybuffer',
      // headers: {
      //   Accept: 'application/octet-stream',
      //   'Content-Type': 'application/octet-stream',
      // },
    },
  );
  return response;
}
  public async putEmpsPhotoImage(
    empsUniqID: string,
    imageId: number,
    model: EmpsPhotoItemResponseModel,
  ): Promise<EmpsPhotoItemResponseModel> {
    const response = await this.http.put<EmpsPhotoItemResponseModel>(
      `emps/${empsUniqID}/child/photo/${imageId}/enclosure/Image`,
      model,
    );
    return response;
  }


public async getEmps(
    empsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<EmpsItemResponseModel> {
    // Construct the API URL using the empsUniqID parameter
    const url = `emps/${empsUniqID}`;

    const response = await this.http.get<EmpsItemResponseModel>(
      url,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      false,
    );
    return response;
  }
  public async postEmpsPhotoList(
    model: EmpsPhotoItemPostRequestModel,
  ): Promise<EmpsPhotoItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<EmpsPhotoItemPostRequestModel>(
      `emps/{empsUniqID}/child/photo`,
      model,
    );
    return response;
  }

  public async updateEmpsPhoto(
    empsUniqID: string,
    imageId: number,
    body: {
      Image: string;
      ImageName: string;
      PrimaryFlag: string;
      ObjectVersionNumber?: number;
    },
    queryParams?: {
      expand?: string;
      fields?: string;
      onlyData?: boolean;
      dependency?: string;
      links?: string;
      [key: string]: any; // in case extra keys come
    },
  ): Promise<EmpsPhotoItemResponseModel> {
    const url = `emps/${empsUniqID}/child/photo/${imageId}`;

    // ✅ Filter out only allowed query parameters
    const allowedQueryKeys = [
      'expand',
      'fields',
      'onlyData',
      'dependency',
      'links',
    ];
    const cleanQueryParams: Record<string, any> = {};

    for (const key of allowedQueryKeys) {
      if (queryParams?.[key] !== undefined) {
        cleanQueryParams[key] = queryParams[key];
      }
    }

    const config = {
      headers: {
        'Content-Type': 'application/vnd.oracle.adf.resourceitem+json',
      },
      params: cleanQueryParams,
    };

    const response = await this.http.patch<EmpsPhotoItemResponseModel>(
      url,
      body,
      config,
    );

    return response;
  }
}
