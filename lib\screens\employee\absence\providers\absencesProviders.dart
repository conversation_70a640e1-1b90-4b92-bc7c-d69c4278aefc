import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';

import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/screens/employee/absence/models/planBalanceSummaryResponse.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/providers/absencesNotifier.dart';
import 'package:seawork/screens/employee/absence/repository/absencesRepository.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailsmodel.dart';

import 'package:seawork/screens/employee/task/models/taskModel.dart';

final leaveTypesRepositoryProvider = ChangeNotifierProvider(
  (ref) => LeaveTypesRepository(ref.read(dioProvider)),
);

final leaveTypesProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final dataList =
      await repository
          .getLeaveTypes(); // Now returns List<Map<String, dynamic>>

  print('Items List: $dataList');

  // Access map values by key
  final absenceTypeNames =
      dataList.map((item) => item['name'].toString()).toList();

  for (var name in absenceTypeNames) {
    print('AbsenceTypeName: $name');
  }

  return absenceTypeNames;
});

final leaveTypeMapProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final dataList = await repository.getLeaveTypes(); // already mapped correctly
  return dataList;
});

final taskTriggerProvider = Provider.autoDispose<bool>((ref) {
  final taskStatus = ref.watch(taskStatusProvider);
  final absenceId = ref.watch(absenceIdProvider);
  // Run task only when both are non-null or valid
  return taskStatus != null && absenceId != 0;
});

final taskItemsProvider = FutureProvider.autoDispose<TaskDetailsModel?>((
  ref,
) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final taskStatus = ref.watch(taskStatusProvider); // Watch task status
  final identificationKey = ref.watch(absenceIdProvider);
  return await repository.getTask(taskStatus, identificationKey);
});
final taskLoadingProvider = FutureProvider<bool>((ref) async {
  // Introduce a delay before returning the loading state
  await Future.delayed(Duration(seconds: 3));

  final taskState = ref.watch(taskItemsProvider);
  return taskState.isLoading;
});

final taskProvider = FutureProvider.family.autoDispose<SingleTask, String>((
  ref,
  identificationKey,
) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  return await repository.getTaskHistory(identificationKey);
});
final leaveTypeTriggerProvider = StateProvider<int>((ref) => 0);
final getAbsenceProvider = FutureProvider.family
    .autoDispose<Map<String, List<AbsenceItem>>, AbsenceQueryParams>((
      ref,
      params,
    ) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.getAbsences(
        offset: params.offset,
        absenceTypeId: params.absenceTypeId,
        dateFilter: params.dateFilter,
        approvalStatusCd: params.approvalStatusCd,
        absenceStatusCd: params.absenceStatusCd,
      );
    });

final uploadAttachmentProvider = FutureProvider.family
    .autoDispose<dynamic, (List<Map<String, Object>>, String)>((
      ref,
      params,
    ) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.uploadAttachment(params.$1, params.$2);
    });

final deleteAttachmentProvider = FutureProvider.family
    .autoDispose<dynamic, (String, String)>((ref, params) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.deleteAbsenceAttachment(params.$1, params.$2);
    });

final getPlanBalanceProvider =
    FutureProvider.autoDispose<List<PlanBalanceModel>>((ref) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.getPlanBalances(false);
    });

final getPlanBalanceSummaryProvider = FutureProvider.family
    .autoDispose<List<PlanBalanceSummaryItemModel>, String>((
      ref,
      personPlanEnrollmentId,
    ) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.getPlanBalanceSummary(personPlanEnrollmentId);
    });

final getAbsenceByUniqIDProvider = FutureProvider.family
    .autoDispose<List<String>, String>((ref, absencesUniqID) async {
      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.getAbsenceByUniqID(absencesUniqID);
    });
// FutureProvider.family for Fetching Attachment File Content
final getAttachmentFileContentProvider = FutureProvider.family
    .autoDispose<dynamic, (dynamic, dynamic)>((ref, params) async {
      final absencesUniqID = params.$1.toString(); // Already a String
      final attachmentId = params.$2.toString(); // Convert int to String

      final repository = ref.read(leaveTypesRepositoryProvider);
      return await repository.getAttachmentFileContent(
        absencesUniqID,
        attachmentId,
      );
    });

final fullAbsenceListProvider = FutureProvider<List<AbsenceItem>>((ref) {
  final repository = ref.read(leaveTypesRepositoryProvider);
  return repository.fullAbsencesList;
});

final totalAwaitingCountProvider = FutureProvider<int>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);
  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["AWAITING"]?.length ?? 0;
});

final selectedLeaveTypeProvider = StateProvider<Map<String, dynamic>?>(
  (ref) => null,
);
final notselectedLeaveTypeProvider = StateProvider<Map<String, dynamic>?>(
  (ref) => null,
);
final selectedSearchLeaveTypeProvider = StateProvider<Map<String, dynamic>?>(
  (ref) => null,
);

final awaitingListProvider = FutureProvider<List<AbsenceItem>>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);
  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["AWAITING"] ?? [];
});

final approvedListProvider = FutureProvider<List<AbsenceItem>>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);
  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["APPROVED"] ?? [];
});
final deniedListProvider = FutureProvider<List<AbsenceItem>>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);

  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["DENIED"] ?? [];
});

final withdrawnListProvider = FutureProvider<List<AbsenceItem>>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);

  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["WITHDRAWN"] ?? [];
});

final draftListProvider = FutureProvider<List<AbsenceItem>>((ref) async {
  final params = ref.watch(selectedAbsenceQueryParamsProvider);

  // final key = selectedType['id'].toString();
  final absences = await ref.watch(getAbsenceProvider(params).future);
  return absences["SAVED"] ?? [];
});

final selectedAbsenceQueryParamsProvider = Provider<AbsenceQueryParams>((ref) {
  final selectedMap = ref.watch(selectedLeaveTypeProvider);

  return AbsenceQueryParams(offset: 0, absenceTypeId: selectedMap!['id']);
});



final taskSearchQueryProvider = StateProvider<String>((ref) => '');
final pendingTasksProvider = FutureProvider.family.autoDispose<TaskListResponse, String>((ref, searchQuery) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final response = await repository.getTasksForAssignToMe(
    status: 'ASSIGNED',
    searchQuery: searchQuery.isEmpty ? null : searchQuery,
  );

  ref.read(taskCountsProvider.notifier).setCount('Awaiting', response.totalResults ?? 0);

  return response;
});
final approvedTasksProvider = FutureProvider.family.autoDispose<TaskListResponse, String>((ref, searchQuery) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final response = await repository.getTasksForAssignToMe(
    status: 'COMPLETED',
    searchQuery: searchQuery.isEmpty ? null : searchQuery,
  );

  ref.read(taskCountsProvider.notifier).setCount('Approved', response.totalResults ?? 0);

  return response;
});
final rejectedTasksProvider = FutureProvider.family.autoDispose<TaskListResponse, String>((ref, searchQuery) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final response = await repository.getTasksForAssignToMe(
    status: 'SUSPENDED',
    searchQuery: searchQuery.isEmpty ? null : searchQuery,
  );

  ref.read(taskCountsProvider.notifier).setCount('Rejected', response.totalResults ?? 0);

  return response;
});

final withdrawnTasksProvider = FutureProvider.family.autoDispose<TaskListResponse, String>((ref, searchQuery) async {
  final repository = ref.read(leaveTypesRepositoryProvider);
  final response = await repository.getTasksForAssignToMe(
    status: 'WITHDRAWN',
    searchQuery: searchQuery.isEmpty ? null : searchQuery,
  );

  ref.read(taskCountsProvider.notifier).setCount('Withdrawn', response.totalResults ?? 0);

  return response;
});


void refreshAllProviders(WidgetRef ref) {
  ref.invalidate(getAbsenceProvider);
  ref.invalidate(leaveTypesProvider);
  ref.invalidate(taskItemsProvider);
  ref.invalidate(taskProvider);
  ref.invalidate(awaitingListProvider);
  ref.invalidate(approvedListProvider);
  ref.invalidate(deniedListProvider);
  ref.invalidate(withdrawnListProvider);
  ref.invalidate(draftListProvider);
  ref.invalidate(getPlanBalanceProvider);
  ref.invalidate(getAbsenceByUniqIDProvider);
  ref.invalidate(getAttachmentFileContentProvider);
  ref.invalidate(fullAbsenceListProvider);
}
