import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { FormattedEmailModel } from '../models/formattedemail/formattedEmail.model';
import { ModuleSetupModel } from '../models/modulesetup/moduleSetup.model';
import { NHD_DeviceInfoModel } from '../models/nhd/nHD_DeviceInfo.model';
import { FaultApprovalModel } from '../models/quotationheader/faultApproval.model';
import { ReleaseProductModel } from '../models/releaseproduct/releaseProduct.model';
import { ServStatusSubscribeModel } from '../models/servstatus/servStatusSubscribe.model';
import { AddressStoreModel } from '../models/workflowtarget/addressStore.model';
import { AreaAzureTenantModel } from '../models/workflowtarget/areaAzureTenant.model';
import { AreaToDoModel } from '../models/workflowtarget/areaToDo.model';
import { AttachmentModel } from '../models/workflowtarget/attachment.model';
import { AuditModel } from '../models/workflowtarget/audit.model';
import { AutoassignModel } from '../models/workflowtarget/autoassign.model';
import { AzureADConnectionModel } from '../models/workflowtarget/azureADConnection.model';
import { AzureADMappingModel } from '../models/workflowtarget/azureADMapping.model';
import { CurrencyModel } from '../models/workflowtarget/currency.model';
import { CustomTableModel } from '../models/workflowtarget/customTable.model';
import { DbTypeModel } from '../models/workflowtarget/dbType.model';
import { DeviceModel } from '../models/workflowtarget/device.model';
import { ExternalLink_ListModel } from '../models/workflowtarget/externalLink_List.model';
import { FAQListHeadModel } from '../models/workflowtarget/fAQListHead.model';
import { FieldGroupModel } from '../models/workflowtarget/fieldGroup.model';
import { FieldInfoModel } from '../models/workflowtarget/fieldInfo.model';
import { IntegrationFieldMappingModel } from '../models/workflowtarget/integrationFieldMapping.model';
import { InvoiceChangeModel } from '../models/workflowtarget/invoiceChange.model';
import { InvoiceDetailModel } from '../models/workflowtarget/invoiceDetail.model';
import { InvoiceDetailProRataModel } from '../models/workflowtarget/invoiceDetailProRata.model';
import { InvoiceHeaderModel } from '../models/workflowtarget/invoiceHeader.model';
import { InvoicePayment_ListModel } from '../models/workflowtarget/invoicePayment_List.model';
import { ItemModel } from '../models/workflowtarget/item.model';
import { ItemStockModel } from '../models/workflowtarget/itemStock.model';
import { ItemSupplierModel } from '../models/workflowtarget/itemSupplier.model';
import { LansweeperSoftwareModel } from '../models/workflowtarget/lansweeperSoftware.model';
import { Licence_ListModel } from '../models/workflowtarget/licence_List.model';
import { LicenceRoleModel } from '../models/workflowtarget/licenceRole.model';
import { MarketingUnsubscribeModel } from '../models/workflowtarget/marketingUnsubscribe.model';
import { PartsLookupModel } from '../models/workflowtarget/partsLookup.model';
import { QualysHostAssetSoftwareHostAssetSoftwareModel } from '../models/workflowtarget/qualysHostAssetSoftwareHostAssetSoftware.model';
import { QuickBooksDetailsModel } from '../models/workflowtarget/quickBooksDetails.model';
import { RequestTypeFieldModel } from '../models/workflowtarget/requestTypeField.model';
import { ServiceRequestDetailsModel } from '../models/workflowtarget/serviceRequestDetails.model';
import { ServiceRestrictionModel } from '../models/workflowtarget/serviceRestriction.model';
import { ServiceUserModel } from '../models/workflowtarget/serviceUser.model';
import { ServSiteModel } from '../models/workflowtarget/servSite.model';
import { ServStatusModel } from '../models/workflowtarget/servStatus.model';
import { TagModel } from '../models/workflowtarget/tag.model';
import { WorkflowTargetModel } from '../models/workflowtarget/workflowTarget.model';
@Injectable()
export class WorkflowtargetService {
  constructor(private readonly http: HttpService) {}

  public async getAddresses(
    count?: number,
    postcode?: string,
    site_id?: number,
    type_id?: number,
    user_id?: number,
    openedafter?: boolean,
    onholdonly?: boolean,
    overrideclientid?: number,
    overridesiteid?: number,
    overrideuserid?: number,
  ): Promise<AddressStoreModel[]> {
    // -- not used
    const response = await this.http.get<AddressStoreModel[]>(
      `Address`,
      {
        count: count,
        postcode: postcode,
        site_id: site_id,
        type_id: type_id,
        user_id: user_id,
        openedafter: openedafter,
        onholdonly: onholdonly,
        overrideclientid: overrideclientid,
        overridesiteid: overridesiteid,
        overrideuserid: overrideuserid,
      },
      true,
    );
    return response;
  }

  public async postAddresses(
    model: AddressStoreModel,
  ): Promise<AddressStoreModel> {
    // -- not used
    const response = await this.http.post<AddressStoreModel>(`Address`, model);
    return response;
  }

  public async getAddressById(
    id: number,
    includedetails?: boolean,
  ): Promise<AddressStoreModel> {
    // -- not used
    const response = await this.http.get<AddressStoreModel>(
      `Address/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getAlembaData(): Promise<AreaToDoModel[]> {
    // -- not used
    const response = await this.http.get<AreaToDoModel[]>(
      `Alemba/Get`,
      {},
      true,
    );
    return response;
  }

  public async postApprovalStoreData(
    model: AreaAzureTenantModel,
  ): Promise<AreaAzureTenantModel> {
    // -- not used
    const response = await this.http.post<AreaAzureTenantModel>(
      `ApprovalStore`,
      model,
    );
    return response;
  }

  public async getAreaAzureTenantData(
    azure_tenant_id?: string,
    client_id?: number,
    details_id?: number,
    ignore_decrypt?: boolean,
    notset?: boolean,
    returnalliflinked?: boolean,
    site_id?: number,
  ): Promise<AreaAzureTenantModel[]> {
    // -- not used
    const response = await this.http.get<AreaAzureTenantModel[]>(
      `AreaAzureTenant`,
      {
        azure_tenant_id: azure_tenant_id,
        client_id: client_id,
        details_id: details_id,
        ignore_decrypt: ignore_decrypt,
        notset: notset,
        returnalliflinked: returnalliflinked,
        site_id: site_id,
      },
      true,
    );
    return response;
  }

  public async getAttachment(
    action_id?: number,
    domotzagents?: boolean,
    filetype?: string,
    idonly?: boolean,
    isxlsimport?: boolean,
    one_attachment_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    portal?: number,
    ticket_id?: number,
    token?: string,
    type?: number,
    unique_id?: number,
  ): Promise<AttachmentModel> {
    // -- not used
    const response = await this.http.get<AttachmentModel>(
      `Attachment`,
      {
        action_id: action_id,
        domotzagents: domotzagents,
        filetype: filetype,
        idonly: idonly,
        isxlsimport: isxlsimport,
        one_attachment_id: one_attachment_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        portal: portal,
        ticket_id: ticket_id,
        token: token,
        type: type,
        unique_id: unique_id,
      },
      false,
    );
    return response;
  }

  public async postAttachment(
    model: AttachmentModel,
  ): Promise<AttachmentModel> {
    // -- not used
    const response = await this.http.post<AttachmentModel>(`Attachment`, model);
    return response;
  }

  public async getAttachmentById(
    id?: string,
    childticketid?: number,
    includedetails?: boolean,
    token?: string,
  ): Promise<{ data: string; filename: string }> {
    const response = await this.http.get<{ data: ArrayBuffer; headers: any }>(
      `attachment/${id}`,
      { childticketid, includedetails, token },
      false,
      {
        responseType: 'arraybuffer',
        transformResponse: (data, headers) => ({ data, headers }),
      },
    );

    const base64Data = Buffer.from(response.data).toString('base64');
    const contentDisposition = response.headers['content-disposition'];
    const filename =
      contentDisposition?.split('filename=')[1]?.replace(/"/g, '') || 'file';

    return {
      data: base64Data,
      filename: filename,
    };
  }

  public async getImageById(id: string): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Attachment/image/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getImage(token?: string, nonce?: string): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Attachment/image`,
      { token: token, nonce: nonce },
      false,
    );
    return response;
  }

  public async postImage(model: any): Promise<any> {
    // -- not used
    const response = await this.http.post(`Attachment/image`, model);
    return response;
  }

  public async getDocumentById(id: number): Promise<AttachmentModel> {
    // -- not used
    const response = await this.http.get<AttachmentModel>(
      `Attachment/document/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async postAllDocuments(
    model: AttachmentModel,
  ): Promise<AttachmentModel> {
    // -- not used
    const response = await this.http.post<AttachmentModel>(
      `Attachment/document`,
      model,
    );
    return response;
  }

  public async getAudit(): Promise<AuditModel[]> {
    // -- not used
    const response = await this.http.get<AuditModel[]>(`Audit`, {}, true);
    return response;
  }

  public async postAudit(model: AuditModel): Promise<AuditModel> {
    // -- not used
    const response = await this.http.post<AuditModel>(`Audit`, model);
    return response;
  }

  public async getAuditById(
    id: number,
    includedetails?: boolean,
  ): Promise<AuditModel> {
    // -- not used
    const response = await this.http.get<AuditModel>(
      `Audit/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getTicketRules(
    access_control_level?: number,
    excludeworkflow?: boolean,
    includecriteriainfo?: boolean,
    isconfig?: boolean,
    rule_use?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `TicketRules`,
      {
        access_control_level: access_control_level,
        excludeworkflow: excludeworkflow,
        includecriteriainfo: includecriteriainfo,
        isconfig: isconfig,
        rule_use: rule_use,
      },
      true,
    );
    return response;
  }

  public async postTicketRules(
    model: AutoassignModel,
  ): Promise<AutoassignModel> {
    // -- not used
    const response = await this.http.post<AutoassignModel>(
      `TicketRules`,
      model,
    );
    return response;
  }

  public async getTicketRuleById(
    id: number,
    includedetails?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `TicketRules/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getAzureAdConnection(
    authorized?: boolean,
    isintune?: boolean,
    type?: number,
    types?: string,
  ): Promise<AzureADConnectionModel[]> {
    // -- not used
    const response = await this.http.get<AzureADConnectionModel[]>(
      `azureadconnection`,
      { authorized: authorized, isintune: isintune, type: type, types: types },
      true,
    );
    return response;
  }

  public async postAzureAdConnection(
    model: AzureADConnectionModel,
  ): Promise<AzureADConnectionModel> {
    // -- not used
    const response = await this.http.post<AzureADConnectionModel>(
      `azureadconnection`,
      model,
    );
    return response;
  }

  public async getAzureAdConnectionById(
    id: number,
    includedetails?: boolean,
    includetenants?: boolean,
  ): Promise<AzureADConnectionModel> {
    // -- not used
    const response = await this.http.get<AzureADConnectionModel>(
      `azureadconnection/${id}`,
      {
        id: id,
        includedetails: includedetails,
        includetenants: includetenants,
      },
      false,
    );
    return response;
  }

  public async getAzureAdMapping(
    connection_id?: number,
  ): Promise<AzureADMappingModel[]> {
    // -- not used
    const response = await this.http.get<AzureADMappingModel[]>(
      `azureadmapping`,
      { connection_id: connection_id },
      true,
    );
    return response;
  }

  public async getCurrency(): Promise<CurrencyModel[]> {
    // -- not used
    const response = await this.http.get<CurrencyModel[]>(`Currency`, {}, true);
    return response;
  }

  public async postCurrency(model: CurrencyModel): Promise<CurrencyModel> {
    // -- not used
    const response = await this.http.post<CurrencyModel>(`Currency`, model);
    return response;
  }

  public async getCurrencyById(
    id: number,
    includedetails?: boolean,
  ): Promise<CurrencyModel> {
    // -- not used
    const response = await this.http.get<CurrencyModel>(
      `Currency/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getCustomTable(
    access_control_level?: number,
    customonly?: boolean,
    isconfig?: boolean,
    iswebhookmapping?: boolean,
    systemonly?: boolean,
    usage?: number,
  ): Promise<CustomTableModel[]> {
    // -- not used
    const response = await this.http.get<CustomTableModel[]>(
      `CustomTable`,
      {
        access_control_level: access_control_level,
        customonly: customonly,
        isconfig: isconfig,
        iswebhookmapping: iswebhookmapping,
        systemonly: systemonly,
        usage: usage,
      },
      true,
    );
    return response;
  }

  public async postCustomTable(
    model: CustomTableModel,
  ): Promise<CustomTableModel> {
    // -- not used
    const response = await this.http.post<CustomTableModel>(
      `CustomTable`,
      model,
    );
    return response;
  }

  public async getCustomTableById(
    id: number,
    includedetails?: boolean,
  ): Promise<CustomTableModel> {
    // -- not used
    const response = await this.http.get<CustomTableModel>(
      `CustomTable/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getAsset(
    activeinactive?: string,
    advanced_search?: string,
    assetgroup_id?: number,
    assetgroups?: string,
    assets?: string,
    assetstatuses?: string,
    assettype?: number,
    assettype_id?: number,
    assettypes?: string,
    bookmarked?: boolean,
    client_id?: number,
    columns_id?: number,
    consignable?: boolean,
    consignment_id?: number,
    contract_id?: number,
    contract_id_adding_to?: number,
    count?: number,
    domotzagents?: boolean,
    excludethese?: string,
    globalSearchID?: string,
    idonly?: boolean,
    includeactive?: boolean,
    includeallowedstatus?: boolean,
    includeassetfields?: boolean,
    includechildren?: boolean,
    includecolumns?: boolean,
    includeinactive?: boolean,
    includeservices?: boolean,
    includeuser?: boolean,
    integration_tenantids?: number[],
    integration_type?: string,
    inventory_number?: string,
    islogonbehalfview?: boolean,
    item_id?: number,
    itemstock_id?: number,
    kb_id?: number,
    lastupdatefromdate?: boolean,
    lastupdatetodate?: boolean,
    licence_id?: number,
    linked_to_ticket?: boolean,
    linkedto_id?: number,
    mine?: boolean,
    mysite?: boolean,
    noicon?: boolean,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    previously_selected?: boolean,
    previously_selected_client_id?: number,
    previously_selected_site_id?: number,
    previously_selected_user_id?: number,
    salesorder_id?: number,
    salesorder_line?: number,
    search?: string,
    search_inventory_number_only?: boolean,
    service_id?: number,
    service_ids?: string,
    site_id?: number,
    stockbin_id?: number,
    stockbin_ids?: number[],
    supplier_contract_id?: number,
    supplier_id?: number,
    suppliercontracts?: string,
    ticket_id?: number,
    tickettype_id?: number,
    user_id?: number,
    username?: string,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Asset`,
      {
        activeinactive: activeinactive,
        advanced_search: advanced_search,
        assetgroup_id: assetgroup_id,
        assetgroups: assetgroups,
        assets: assets,
        assetstatuses: assetstatuses,
        assettype: assettype,
        assettype_id: assettype_id,
        assettypes: assettypes,
        bookmarked: bookmarked,
        client_id: client_id,
        columns_id: columns_id,
        consignable: consignable,
        consignment_id: consignment_id,
        contract_id: contract_id,
        contract_id_adding_to: contract_id_adding_to,
        count: count,
        domotzagents: domotzagents,
        excludethese: excludethese,
        globalSearchID: globalSearchID,
        idonly: idonly,
        includeactive: includeactive,
        includeallowedstatus: includeallowedstatus,
        includeassetfields: includeassetfields,
        includechildren: includechildren,
        includecolumns: includecolumns,
        includeinactive: includeinactive,
        includeservices: includeservices,
        includeuser: includeuser,
        integration_tenantids: integration_tenantids,
        integration_type: integration_type,
        inventory_number: inventory_number,
        islogonbehalfview: islogonbehalfview,
        item_id: item_id,
        itemstock_id: itemstock_id,
        kb_id: kb_id,
        lastupdatefromdate: lastupdatefromdate,
        lastupdatetodate: lastupdatetodate,
        licence_id: licence_id,
        linked_to_ticket: linked_to_ticket,
        linkedto_id: linkedto_id,
        mine: mine,
        mysite: mysite,
        noicon: noicon,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        previously_selected: previously_selected,
        previously_selected_client_id: previously_selected_client_id,
        previously_selected_site_id: previously_selected_site_id,
        previously_selected_user_id: previously_selected_user_id,
        salesorder_id: salesorder_id,
        salesorder_line: salesorder_line,
        search: search,
        search_inventory_number_only: search_inventory_number_only,
        service_id: service_id,
        service_ids: service_ids,
        site_id: site_id,
        stockbin_id: stockbin_id,
        stockbin_ids: stockbin_ids,
        supplier_contract_id: supplier_contract_id,
        supplier_id: supplier_id,
        suppliercontracts: suppliercontracts,
        ticket_id: ticket_id,
        tickettype_id: tickettype_id,
        user_id: user_id,
        username: username,
      },
      true,
    );
    return response;
  }

  public async postAsset(model: DeviceModel): Promise<DeviceModel> {
    // -- not used
    const response = await this.http.post<DeviceModel>(`Asset`, model);
    return response;
  }

  public async getAssetById(
    id: number,
    // client_id :number,
    assettype_id?: number,
    includeactivity?: boolean,
    includeallowedstatus?: boolean,
    includedetails?: boolean,
    includediagramdetails?: boolean,
    includehierarchy?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Asset/${id}`,
      {
        id: id,
        // client_id:client_id,
        assettype_id: assettype_id,
        includeactivity: includeactivity,
        includeallowedstatus: includeallowedstatus,
        includedetails: includedetails,
        includediagramdetails: includediagramdetails,
        includehierarchy: includehierarchy,
      },
      false,
    );
    return response;
  }

  // ....
  public async getAssetClById(
    // id: number,
    client_id: number,
    assettype_id?: number,
    includeactivity?: boolean,
    includeallowedstatus?: boolean,
    includedetails?: boolean,
    includediagramdetails?: boolean,
    includehierarchy?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Asset/${client_id}`,
      {
        // id: id,
        client_id: client_id,
        assettype_id: assettype_id,
        includeactivity: includeactivity,
        includeallowedstatus: includeallowedstatus,
        includedetails: includedetails,
        includediagramdetails: includediagramdetails,
        includehierarchy: includehierarchy,
      },
      false,
    );
    return response;
  }

  public async getAssetNextTag(): Promise<any> {
    // -- not used
    const response = await this.http.get(`Asset/NextTag`, {}, false);
    return response;
  }

  public async getAssetGetAllSoftwareVersions(): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Asset/GetAllSoftwareVersions`,
      {},
      true,
    );
    return response;
  }

  public async getAssetSoftware(
    device_id?: number,
    licence_id?: number,
    third_party_field?: string,
    third_party_id?: string,
    user_id?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `AssetSoftware`,
      {
        device_id: device_id,
        licence_id: licence_id,
        third_party_field: third_party_field,
        third_party_id: third_party_id,
        user_id: user_id,
      },
      true,
    );
    return response;
  }

  public async getDeviceLicence(): Promise<any> {
    // -- not used
    const response = await this.http.get(`DeviceLicence`, {}, true);
    return response;
  }

  public async getExternalLink(
    count?: number,
    details_id?: number,
    halo_id?: number,
    module_id?: number,
    module_list?: string,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    search?: string,
    table_id?: number,
    third_party_desc?: string,
    third_party_id?: string,
    third_party_secondary_id?: string,
    third_party_type?: string,
  ): Promise<ExternalLink_ListModel[]> {
    // -- not used
    const response = await this.http.get<ExternalLink_ListModel[]>(
      `ExternalLink`,
      {
        count: count,
        details_id: details_id,
        halo_id: halo_id,
        module_id: module_id,
        module_list: module_list,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        search: search,
        table_id: table_id,
        third_party_desc: third_party_desc,
        third_party_id: third_party_id,
        third_party_secondary_id: third_party_secondary_id,
        third_party_type: third_party_type,
      },
      true,
    );
    return response;
  }

  public async postExternalLink(
    model: ExternalLink_ListModel,
  ): Promise<ExternalLink_ListModel> {
    // -- not used
    const response = await this.http.post<ExternalLink_ListModel>(
      `ExternalLink`,
      model,
    );
    return response;
  }

  public async getExternalLinkById(
    id: number,
    includedetails?: boolean,
  ): Promise<ExternalLink_ListModel> {
    // -- not used
    const response = await this.http.get<ExternalLink_ListModel>(
      `ExternalLink/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getFAQLists(
    allgroups?: boolean,
    endoftreeonly?: boolean,
    level?: number,
    organisation_id?: number,
    parent_id?: number,
    showcounts?: boolean,
    type?: number,
  ): Promise<FAQListHeadModel[]> {
    // -- not used
    const response = await this.http.get<FAQListHeadModel[]>(
      `FAQLists`,
      {
        allgroups: allgroups,
        endoftreeonly: endoftreeonly,
        level: level,
        organisation_id: organisation_id,
        parent_id: parent_id,
        showcounts: showcounts,
        type: type,
      },
      true,
    );
    return response;
  }

  public async postFAQLists(
    model: FAQListHeadModel,
  ): Promise<FAQListHeadModel> {
    // -- not used
    const response = await this.http.post<FAQListHeadModel>(`FAQLists`, model);
    return response;
  }

  public async getFAQListsById(
    id: number,
    includedetails?: boolean,
    organisation_id?: number,
  ): Promise<FAQListHeadModel[]> {
    // -- not used
    const response = await this.http.get<FAQListHeadModel[]>(
      `FAQLists/${id}`,
      {
        id: id,
        includedetails: includedetails,
        organisation_id: organisation_id,
      },
      true,
    );
    return response;
  }

  public async getTicketApproval(params: {
    action_number?: number;
    include_agent_details?: boolean;
    include_attachments?: boolean;
    includeapprovaldetails?: boolean;
    mine?: boolean;
    ticket_id?: number;
  }): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `TicketApproval`,
      {
        action_number: params.action_number,
        include_agent_details: params.include_agent_details,
        include_attachments: params.include_attachments,
        includeapprovaldetails: params.includeapprovaldetails,
        mine: params.mine,
        ticket_id: params.ticket_id,
      },
      true,
    );
    return response;
  }

  public async postTicketApproval(
    model: FaultApprovalModel,
  ): Promise<FaultApprovalModel> {
    // -- not used
    const response = await this.http.post<FaultApprovalModel>(
      `TicketApproval`,
      model,
    );
    return response;
  }

  public async getTicketApprovalById(
    id: number,
    includedetails?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `TicketApproval/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getFieldGroup(
    access_control_level?: number,
    includefields?: boolean,
    isconfig?: boolean,
  ): Promise<FieldGroupModel[]> {
    // -- not used
    const response = await this.http.get<FieldGroupModel[]>(
      `FieldGroup`,
      {
        access_control_level: access_control_level,
        includefields: includefields,
        isconfig: isconfig,
      },
      true,
    );
    return response;
  }

  public async postFieldGroup(
    model: FieldGroupModel,
  ): Promise<FieldGroupModel> {
    // -- not used
    const response = await this.http.post<FieldGroupModel>(`FieldGroup`, model);
    return response;
  }

  public async getFieldGroupById(
    id: number,
    includedetails?: boolean,
  ): Promise<FieldGroupModel> {
    // -- not used
    const response = await this.http.get<FieldGroupModel>(
      `FieldGroup/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getFieldInfo(
    access_control_level?: number,
    domain?: string,
    excluderanges?: boolean,
    excludetables?: boolean,
    excludetableself?: boolean,
    extratype?: number,
    fieldtype?: number,
    fieldtypemultiple?: string,
    includecategories?: boolean,
    includedatefields?: boolean,
    includejirafields?: boolean,
    includeremotefields?: boolean,
    includevalues?: boolean,
    inputtype?: number,
    isapprovalstep?: boolean,
    isconfig?: boolean,
    iscustomfieldsetup?: boolean,
    systemid?: number,
    typeid?: number,
  ): Promise<FieldInfoModel[]> {
    // -- not used
    const response = await this.http.get<FieldInfoModel[]>(
      `FieldInfo`,
      {
        access_control_level: access_control_level,
        domain: domain,
        excluderanges: excluderanges,
        excludetables: excludetables,
        excludetableself: excludetableself,
        extratype: extratype,
        fieldtype: fieldtype,
        fieldtypemultiple: fieldtypemultiple,
        includecategories: includecategories,
        includedatefields: includedatefields,
        includejirafields: includejirafields,
        includeremotefields: includeremotefields,
        includevalues: includevalues,
        inputtype: inputtype,
        isapprovalstep: isapprovalstep,
        isconfig: isconfig,
        iscustomfieldsetup: iscustomfieldsetup,
        systemid: systemid,
        typeid: typeid,
      },
      true,
    );
    return response;
  }

  public async postFieldInfo(model: FieldInfoModel): Promise<FieldInfoModel> {
    // -- not used
    const response = await this.http.post<FieldInfoModel>(`FieldInfo`, model);
    return response;
  }

  public async getFieldInfoById(
    id: number,
    entityid?: number,
    getlookupvalues?: boolean,
    includedetails?: boolean,
    livecustomfields?: string,
    userid?: number,
  ): Promise<FieldInfoModel> {
    // -- not used
    const response = await this.http.get<FieldInfoModel>(
      `FieldInfo/${id}`,
      {
        id: id,
        entityid: entityid,
        getlookupvalues: getlookupvalues,
        includedetails: includedetails,
        livecustomfields: livecustomfields,
        userid: userid,
      },
      false,
    );
    return response;
  }

  public async getLansweeper(
    datatype?: string,
    exportid?: string,
    exportUrl?: string,
    halositeid?: number,
    siteid?: string,
  ): Promise<LansweeperSoftwareModel> {
    // -- not used
    const response = await this.http.get<LansweeperSoftwareModel>(
      `IntegrationData/Get/Lansweeper`,
      {
        datatype: datatype,
        exportid: exportid,
        exportUrl: exportUrl,
        halositeid: halositeid,
        siteid: siteid,
      },
      false,
    );
    return response;
  }

  public async getQualys(
    mappingid?: number,
    offset?: number,
    paginate?: boolean,
    resource?: string,
  ): Promise<QualysHostAssetSoftwareHostAssetSoftwareModel> {
    // -- not used
    const response =
      await this.http.get<QualysHostAssetSoftwareHostAssetSoftwareModel>(
        `IntegrationData/Get/Qualys`,
        {
          mappingid: mappingid,
          offset: offset,
          paginate: paginate,
          resource: resource,
        },
        false,
      );
    return response;
  }

  public async getIntegrationFieldMapping(
    msid?: string,
    product_id?: number,
    subtypeid?: string,
    syncfields?: boolean,
    typeid?: string,
    xmvalue?: string,
  ): Promise<IntegrationFieldMappingModel> {
    // -- not used
    const response = await this.http.get<IntegrationFieldMappingModel>(
      `IntegrationFieldMapping`,
      {
        msid: msid,
        product_id: product_id,
        subtypeid: subtypeid,
        syncfields: syncfields,
        typeid: typeid,
        xmvalue: xmvalue,
      },
      false,
    );
    return response;
  }

  public async getInvoiceChange(
    count?: number,
    idonly?: boolean,
    invoice_id?: number,
    line_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    search?: string,
    type_id?: number,
  ): Promise<InvoiceChangeModel> {
    // -- not used
    const response = await this.http.get<InvoiceChangeModel>(
      `InvoiceChange`,
      {
        count: count,
        idonly: idonly,
        invoice_id: invoice_id,
        line_id: line_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        search: search,
        type_id: type_id,
      },
      false,
    );
    return response;
  }

  public async postInvoiceChange(
    model: InvoiceChangeModel,
  ): Promise<InvoiceChangeModel> {
    // -- not used
    const response = await this.http.post<InvoiceChangeModel>(
      `InvoiceChange`,
      model,
    );
    return response;
  }

  public async getInvoiceDetailProRata(): Promise<InvoiceDetailProRataModel> {
    // -- not used
    const response = await this.http.get<InvoiceDetailProRataModel>(
      `InvoiceDetailProRata`,
      {},
      false,
    );
    return response;
  }

  public async getInvoice(
    advanced_search?: string,
    asset_id?: number,
    awaiting_approval?: boolean,
    billing_date?: string,
    billingcategory_ids?: string,
    client_id?: number,
    client_ids?: string,
    contract_id?: number,
    count?: number,
    idonly?: boolean,
    includecredits?: boolean,
    includeinvoices?: boolean,
    includelines?: boolean,
    includepoinvoices?: boolean,
    invoicedateend?: boolean,
    invoicedatestart?: boolean,
    my_approvals?: boolean,
    notpostedonly?: boolean,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    paymentstatuses?: number[],
    postedonly?: boolean,
    purchaseorder_id?: number,
    quote_status?: string,
    ready_for_invoicing?: boolean,
    recurringinvoice_id?: number,
    reviewrequired?: boolean,
    rinvoice_type?: string,
    salesorder_id?: number,
    search?: string,
    sent_status?: number,
    site_id?: number,
    stripeautopaymentrequired?: boolean,
    ticket_id?: number,
    toplevel_id?: number,
    user_id?: number,
    third_party_id?: string,
    xero_id?: string,
    quickbooks_id?: number,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    const response = await this.http.get<InvoiceHeaderModel>(
      `Invoice`,
      {
        advanced_search: advanced_search,
        asset_id: asset_id,
        awaiting_approval: awaiting_approval,
        billing_date: billing_date,
        billingcategory_ids: billingcategory_ids,
        client_id: client_id,
        client_ids: client_ids,
        contract_id: contract_id,
        count: count,
        idonly: idonly,
        includecredits: includecredits,
        includeinvoices: includeinvoices,
        includelines: includelines,
        includepoinvoices: includepoinvoices,
        invoicedateend: invoicedateend,
        invoicedatestart: invoicedatestart,
        my_approvals: my_approvals,
        notpostedonly: notpostedonly,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        paymentstatuses: paymentstatuses,
        postedonly: postedonly,
        purchaseorder_id: purchaseorder_id,
        quote_status: quote_status,
        ready_for_invoicing: ready_for_invoicing,
        recurringinvoice_id: recurringinvoice_id,
        reviewrequired: reviewrequired,
        rinvoice_type: rinvoice_type,
        salesorder_id: salesorder_id,
        search: search,
        sent_status: sent_status,
        site_id: site_id,
        stripeautopaymentrequired: stripeautopaymentrequired,
        ticket_id: ticket_id,
        toplevel_id: toplevel_id,
        user_id: user_id,
        third_party_id: third_party_id,
        xero_id: xero_id,
        quickbooks_id: quickbooks_id,
      },
      false,
    );
    return response;
  }

  public async postInvoice(
    model: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    const response = await this.http.post<InvoiceHeaderModel>(`Invoice`, model);
    return response;
  }

  public async getInvoiceById(
    id: number,
    includedetails?: boolean,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    const response = await this.http.get<InvoiceHeaderModel>(
      `Invoice/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async postInvoiceUpdatelines(
    model: InvoiceDetailModel,
  ): Promise<InvoiceDetailModel> {
    // -- not used
    const response = await this.http.post<InvoiceDetailModel>(
      `Invoice/updatelines`,
      model,
    );
    return response;
  }

  public async postInvoiceVoidById(
    model: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    const response = await this.http.post<InvoiceHeaderModel>(
      `Invoice/{id}/void`,
      model,
    );
    return response;
  }

  public async postInvoicePdfById(
    model: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    const response = await this.http.post<InvoiceHeaderModel>(
      `Invoice/PDF/{id}`,
      model,
    );
    return response;
  }

  public async getInvoicePayment(
    client_id?: number,
    count?: number,
    intent_id?: string,
    invoice_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    search?: string,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    const response = await this.http.get<InvoicePayment_ListModel>(
      `InvoicePayment`,
      {
        client_id: client_id,
        count: count,
        intent_id: intent_id,
        invoice_id: invoice_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        search: search,
      },
      false,
    );
    return response;
  }

  public async postInvoicePayment(
    model: InvoicePayment_ListModel,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    const response = await this.http.post<InvoicePayment_ListModel>(
      `InvoicePayment`,
      model,
    );
    return response;
  }

  public async getInvoicePaymentById(
    id: number,
    includedetails?: boolean,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    const response = await this.http.get<InvoicePayment_ListModel>(
      `InvoicePayment/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getItem(
    activeinactive?: string,
    advanced_search?: string,
    assetgroup_id?: number,
    assetgroups?: string,
    assettypes?: string,
    autotask_service_items?: boolean,
    count?: number,
    dbc_company_id?: string,
    exactdivision?: number,
    excluderecurring?: boolean,
    includeactive?: boolean,
    includeinactive?: boolean,
    include_custom_fields?: string,
    itemservice_id?: number,
    itemservicerequestdetails_id?: number,
    itemsupplierclientid?: number,
    kashflowtenantid?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    qbitemsonly?: boolean,
    qbocompanyid?: string,
    recurringonly?: boolean,
    sagebusinesscloudtenantid?: number,
    search?: string,
    search1?: string,
    show_not_in_stock?: boolean,
    stocklocation_id?: number,
    supplier_id?: number,
    xerotenantid?: string,
  ): Promise<ItemModel> {
    // -- not used
    const response = await this.http.get<ItemModel>(
      `Item`,
      {
        activeinactive: activeinactive,
        advanced_search: advanced_search,
        assetgroup_id: assetgroup_id,
        assetgroups: assetgroups,
        assettypes: assettypes,
        autotask_service_items: autotask_service_items,
        count: count,
        dbc_company_id: dbc_company_id,
        exactdivision: exactdivision,
        excluderecurring: excluderecurring,
        includeactive: includeactive,
        includeinactive: includeinactive,
        include_custom_fields: include_custom_fields,
        itemservice_id: itemservice_id,
        itemservicerequestdetails_id: itemservicerequestdetails_id,
        itemsupplierclientid: itemsupplierclientid,
        kashflowtenantid: kashflowtenantid,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        qbitemsonly: qbitemsonly,
        qbocompanyid: qbocompanyid,
        recurringonly: recurringonly,
        sagebusinesscloudtenantid: sagebusinesscloudtenantid,
        search: search,
        search1: search1,
        show_not_in_stock: show_not_in_stock,
        stocklocation_id: stocklocation_id,
        supplier_id: supplier_id,
        xerotenantid: xerotenantid,
      },
      false,
    );
    return response;
  }

  public async postItem(model: ItemModel): Promise<ItemModel> {
    // -- not used
    const response = await this.http.post<ItemModel>(`Item`, model);
    return response;
  }

  public async getItemById(
    id: number,
    dbc_company_id?: string,
    includedetails?: boolean,
    kashflowtenantid?: number,
    qbocompanyid?: string,
    sagebusinesscloudtenantid?: number,
    xerotenantid?: string,
  ): Promise<ItemModel> {
    // -- not used
    const response = await this.http.get<ItemModel>(
      `Item/${id}`,
      {
        id: id,
        dbc_company_id: dbc_company_id,
        includedetails: includedetails,
        kashflowtenantid: kashflowtenantid,
        qbocompanyid: qbocompanyid,
        sagebusinesscloudtenantid: sagebusinesscloudtenantid,
        xerotenantid: xerotenantid,
      },
      false,
    );
    return response;
  }

  public async postItemNewAccountsId(model: ItemModel): Promise<ItemModel> {
    // -- not used
    const response = await this.http.post<ItemModel>(
      `Item/NewAccountsId`,
      model,
    );
    return response;
  }

  public async getItemStock(
    count?: number,
    idonly?: boolean,
    item_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    stockbin_id?: number,
    stocklocation_id?: number,
  ): Promise<ItemStockModel> {
    // -- not used
    const response = await this.http.get<ItemStockModel>(
      `ItemStock`,
      {
        count: count,
        idonly: idonly,
        item_id: item_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        stockbin_id: stockbin_id,
        stocklocation_id: stocklocation_id,
      },
      false,
    );
    return response;
  }

  public async postItemStock(model: ItemStockModel): Promise<ItemStockModel> {
    // -- not used
    const response = await this.http.post<ItemStockModel>(`ItemStock`, model);
    return response;
  }

  public async getItemStockById(
    id: number,
    includedetails?: boolean,
  ): Promise<ItemStockModel> {
    // -- not used
    const response = await this.http.get<ItemStockModel>(
      `ItemStock/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getItemStockHistory(
    count?: number,
    idonly?: boolean,
    item_id?: number,
    itemstock_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    search?: string,
    stockbin_id?: number,
    stocklocation_id?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `ItemStockHistory`,
      {
        count: count,
        idonly: idonly,
        item_id: item_id,
        itemstock_id: itemstock_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        search: search,
        stockbin_id: stockbin_id,
        stocklocation_id: stocklocation_id,
      },
      false,
    );
    return response;
  }

  public async getItemStockHistoryById(id: number): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `ItemStockHistory/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getItemsupplier(): Promise<ItemSupplierModel> {
    // -- not used
    const response = await this.http.get<ItemSupplierModel>(
      `itemsupplier`,
      {},
      false,
    );
    return response;
  }

  public async postItemsupplier(
    model: ItemSupplierModel,
  ): Promise<ItemSupplierModel> {
    // -- not used
    const response = await this.http.post<ItemSupplierModel>(
      `itemsupplier`,
      model,
    );
    return response;
  }

  public async getItemsupplierById(
    id: number,
    includedetails?: boolean,
  ): Promise<ItemSupplierModel> {
    // -- not used
    const response = await this.http.get<ItemSupplierModel>(
      `itemsupplier/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getSoftwareLicenceById(
    id: number,
    includedetails?: boolean,
  ): Promise<Licence_ListModel> {
    // -- not used
    const response = await this.http.get<Licence_ListModel>(
      `SoftwareLicence/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getSoftwareLicenceRole(
    softwarelicence_id?: number,
  ): Promise<LicenceRoleModel> {
    // -- not used
    const response = await this.http.get<LicenceRoleModel>(
      `SoftwareLicenceRole`,
      { softwarelicence_id: softwarelicence_id },
      false,
    );
    return response;
  }

  public async getMailCampaignLog(): Promise<FormattedEmailModel> {
    // -- not used
    const response = await this.http.get<FormattedEmailModel>(
      `MailCampaignLog`,
      {},
      false,
    );
    return response;
  }

  public async getMailCampaignLogById(
    id: number,
  ): Promise<FormattedEmailModel> {
    // -- not used
    const response = await this.http.get<FormattedEmailModel>(
      `MailCampaignLog/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getMarketingUnsubscribe(): Promise<MarketingUnsubscribeModel> {
    // -- not used
    const response = await this.http.get<MarketingUnsubscribeModel>(
      `MarketingUnsubscribe`,
      {},
      false,
    );
    return response;
  }

  public async postMarketingUnsubscribe(
    model: MarketingUnsubscribeModel,
  ): Promise<MarketingUnsubscribeModel> {
    // -- not used
    const response = await this.http.post<MarketingUnsubscribeModel>(
      `MarketingUnsubscribe`,
      model,
    );
    return response;
  }

  public async getMarketingUnsubscribeById(
    id: number,
  ): Promise<MarketingUnsubscribeModel> {
    // -- not used
    const response = await this.http.get<MarketingUnsubscribeModel>(
      `MarketingUnsubscribe/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getFeatures(
    isconfig?: boolean,
    showdisabled?: boolean,
    showenabled?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Features`,
      {
        isconfig: isconfig,
        showdisabled: showdisabled,
        showenabled: showenabled,
      },
      false,
    );
    return response;
  }

  public async postFeatures(
    model: ModuleSetupModel,
  ): Promise<ModuleSetupModel> {
    // -- not used
    const response = await this.http.post<ModuleSetupModel>(`Features`, model);
    return response;
  }

  public async getFeaturesById(
    id: number,
    includedetails?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Features/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getHaloDeviceInfoById(id: string): Promise<DeviceModel> {
    // -- not used
    const response = await this.http.get<DeviceModel>(
      `HaloDeviceInfo/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async postHaloDeviceInfo(
    model: NHD_DeviceInfoModel,
  ): Promise<NHD_DeviceInfoModel> {
    // -- not used
    const response = await this.http.post<NHD_DeviceInfoModel>(
      `HaloDeviceInfo`,
      model,
    );
    return response;
  }

  public async getOutgoing(
    count?: number,
    idonly?: boolean,
    mailbox_id?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    status_id?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Outgoing`,
      {
        count: count,
        idonly: idonly,
        mailbox_id: mailbox_id,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        status_id: status_id,
      },
      true,
    );
    return response;
  }

  public async postOutgoing(model: any): Promise<any> {
    // -- not used
    const response = await this.http.post(`Outgoing`, model);
    return response;
  }

  public async getOutgoingById(
    id: number,
    includeattachments?: boolean,
    includedetails?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Outgoing/${id}`,
      {
        id: id,
        includeattachments: includeattachments,
        includedetails: includedetails,
      },
      false,
    );
    return response;
  }

  public async getOutgoingAttempt(
    count?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    outgoing_id?: number,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `OutgoingAttempt`,
      {
        count: count,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        outgoing_id: outgoing_id,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
      },
      true,
    );
    return response;
  }

  public async getOutgoingAttemptById(
    id: number,
    includeattachments?: boolean,
    includedetails?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `OutgoingAttempt/${id}`,
      {
        id: id,
        includeattachments: includeattachments,
        includedetails: includedetails,
      },
      false,
    );
    return response;
  }

  public async getDatabaseLookup(type?: number): Promise<DbTypeModel[]> {
    // -- not used
    const response = await this.http.get<DbTypeModel[]>(
      `DatabaseLookup`,
      { type: type },
      true,
    );
    return response;
  }

  public async postDatabaseLookup(
    model: PartsLookupModel,
  ): Promise<PartsLookupModel> {
    // -- not used
    const response = await this.http.post<PartsLookupModel>(
      `DatabaseLookup`,
      model,
    );
    return response;
  }

  public async getDatabaseLookupById(
    id: number,
    includedetails?: boolean,
    lookup_value?: object,
  ): Promise<DbTypeModel> {
    // -- not used
    const response = await this.http.get<DbTypeModel>(
      `DatabaseLookup/${id}`,
      { id: id, includedetails: includedetails, lookup_value: lookup_value },
      false,
    );
    return response;
  }

  public async postDatabaseLookupRun(
    model: PartsLookupModel,
  ): Promise<PartsLookupModel> {
    // -- not used
    const response = await this.http.post<PartsLookupModel>(
      `DatabaseLookup/run`,
      model,
    );
    return response;
  }

  public async getQuickBooksDetails(
    companyid?: string,
    connectedonly?: boolean,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    const response = await this.http.get<QuickBooksDetailsModel>(
      `QuickBooksDetails`,
      { companyid: companyid, connectedonly: connectedonly },
      false,
    );
    return response;
  }

  public async postQuickBooksDetails(
    model: QuickBooksDetailsModel,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    const response = await this.http.post<QuickBooksDetailsModel>(
      `QuickBooksDetails`,
      model,
    );
    return response;
  }

  public async getQuickBooksDetailsById(
    id: number,
    includedetails?: boolean,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    const response = await this.http.get<QuickBooksDetailsModel>(
      `QuickBooksDetails/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getProduct(
    devops_instance?: number,
    third_party_only?: boolean,
  ): Promise<ItemModel> {
    // -- not used
    const response = await this.http.get<ItemModel>(
      `Product`,
      { devops_instance: devops_instance, third_party_only: third_party_only },
      false,
    );
    return response;
  }

  public async postProduct(
    model: ReleaseProductModel,
  ): Promise<ReleaseProductModel> {
    // -- not used
    const response = await this.http.post<ReleaseProductModel>(
      `Product`,
      model,
    );
    return response;
  }

  public async getProductById(
    id: number,
    includedetails?: boolean,
  ): Promise<ItemModel> {
    // -- not used
    const response = await this.http.get<ItemModel>(
      `Product/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getTicketTypeField(
    buildcache?: boolean,
    debug?: boolean,
    isrtconfig?: boolean,
  ): Promise<RequestTypeFieldModel> {
    // -- not used
    const response = await this.http.get<RequestTypeFieldModel>(
      `TicketTypeField`,
      { buildcache: buildcache, debug: debug, isrtconfig: isrtconfig },
      false,
    );
    return response;
  }

  public async getRoadmap(
    halocrm?: boolean,
    haloitsm?: boolean,
    halopsa?: boolean,
    haloservicedesk?: boolean,
    order?: string,
    orderdesc?: boolean,
    product_id?: number,
    roadmapcolumnview?: boolean,
  ): Promise<WorkflowTargetModel> {
    // -- not used
    const response = await this.http.get<WorkflowTargetModel>(
      `Roadmap`,
      {
        halocrm: halocrm,
        haloitsm: haloitsm,
        halopsa: halopsa,
        haloservicedesk: haloservicedesk,
        order: order,
        orderdesc: orderdesc,
        product_id: product_id,
        roadmapcolumnview: roadmapcolumnview,
      },
      false,
    );
    return response;
  }

  public async getScheduleOccurrence(): Promise<AuditModel> {
    // -- not used
    const response = await this.http.get<AuditModel>(
      `ScheduleOccurrence`,
      {},
      false,
    );
    return response;
  }

  public async postScheduleOccurrence(model: AuditModel): Promise<AuditModel> {
    // -- not used
    const response = await this.http.post<AuditModel>(
      `ScheduleOccurrence`,
      model,
    );
    return response;
  }

  public async getScheduleOccurrenceById(id: number): Promise<AuditModel> {
    // -- not used
    const response = await this.http.get<AuditModel>(
      `ScheduleOccurrence/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getServiceRequestDetails(
    exclude_urls?: boolean,
    includedetails?: boolean,
    service_id?: number,
  ): Promise<ServiceRequestDetailsModel> {
    // -- not used
    const response = await this.http.get<ServiceRequestDetailsModel>(
      `ServiceRequestDetails`,
      {
        exclude_urls: exclude_urls,
        includedetails: includedetails,
        service_id: service_id,
      },
      false,
    );
    return response;
  }

  public async getServiceRequestDetailsById(
    id: number,
    includedetails?: boolean,
  ): Promise<ServiceRequestDetailsModel> {
    // -- not used
    const response = await this.http.get<ServiceRequestDetailsModel>(
      `ServiceRequestDetails/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getServiceRestriction(
    client_id?: number,
    service_category_id?: number,
    service_id?: number,
  ): Promise<ServiceRestrictionModel> {
    // -- not used
    const response = await this.http.get<ServiceRestrictionModel>(
      `ServiceRestriction`,
      {
        client_id: client_id,
        service_category_id: service_category_id,
        service_id: service_id,
      },
      false,
    );
    return response;
  }

  public async getService(
    access_control_level?: number,
    asset_ids?: string,
    count?: number,
    includechildservices?: boolean,
    includestatusinfo?: boolean,
    itil_ticket_type?: number,
    monitoredonly?: boolean,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    parent_service_category_id?: number,
    relatedservicesonly?: boolean,
    search?: string,
    service_category_id?: number,
    service_category_ids?: string,
    service_status_ids?: string,
    subscribedonly?: boolean,
    template_id?: number,
    ticket_id?: number,
    tickettype_id?: number,
    user_id?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get<any>(
      `Service`,
      {
        access_control_level: access_control_level,
        asset_ids: asset_ids,
        count: count,
        includechildservices: includechildservices,
        includestatusinfo: includestatusinfo,
        itil_ticket_type: itil_ticket_type,
        monitoredonly: monitoredonly,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        parent_service_category_id: parent_service_category_id,
        relatedservicesonly: relatedservicesonly,
        search: search,
        service_category_id: service_category_id,
        service_category_ids: service_category_ids,
        service_status_ids: service_status_ids,
        subscribedonly: subscribedonly,
        template_id: template_id,
        ticket_id: ticket_id,
        tickettype_id: tickettype_id,
        user_id: user_id,
      },
      false,
    );
    return response;
  }

  public async postService(model: ServSiteModel): Promise<ServSiteModel> {
    // -- not used
    const response = await this.http.post<ServSiteModel>(`Service`, model);
    return response;
  }

  public async getServiceById(
    id: number,
    includedetails?: boolean,
    user_id?: number,
  ): Promise<ServiceUserModel> {
    // -- not used
    const response = await this.http.get<ServiceUserModel>(
      `Service/${id}`,
      { id: id, includedetails: includedetails, user_id: user_id },
      false,
    );
    return response;
  }

  public async getServiceStatus(
    count?: number,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    service_id?: number,
  ): Promise<ServStatusModel> {
    // -- not used
    const response = await this.http.get<ServStatusModel>(
      `ServiceStatus`,
      {
        count: count,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        service_id: service_id,
      },
      false,
    );
    return response;
  }

  public async postServiceStatus(
    model: ServStatusModel,
  ): Promise<ServStatusModel> {
    // -- not used
    const response = await this.http.post<ServStatusModel>(
      `ServiceStatus`,
      model,
    );
    return response;
  }

  public async getSubscribeServiceStatusById(
    id: number,
  ): Promise<ServStatusSubscribeModel> {
    // -- not used
    const response = await this.http.get<ServStatusSubscribeModel>(
      `ServiceStatus/Subscribe/${id}`,
      { id: id },
      false,
    );
    return response;
  }

  public async getServiceStatusById(
    id: string,
    includedetails?: boolean,
  ): Promise<ServStatusModel> {
    // -- not used
    const response = await this.http.get<ServStatusModel>(
      `ServiceStatus/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async postSubscribeServiceStatus(
    model: ServStatusSubscribeModel,
  ): Promise<ServStatusSubscribeModel> {
    // -- not used
    const response = await this.http.post<ServStatusSubscribeModel>(
      `ServiceStatus/Subscribe`,
      model,
    );
    return response;
  }

  public async getTags(): Promise<TagModel> {
    // -- not used
    const response = await this.http.get<TagModel>(`Tags`, {}, false);
    return response;
  }

  public async postTags(model: TagModel): Promise<TagModel> {
    // -- not used
    const response = await this.http.post<TagModel>(`Tags`, model);
    return response;
  }

  public async getTagsById(
    id: number,
    includedetails?: boolean,
  ): Promise<TagModel> {
    // -- not used
    const response = await this.http.get<TagModel>(
      `Tags/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }

  public async getWorkflowTarget(): Promise<WorkflowTargetModel> {
    // -- not used
    const response = await this.http.get<WorkflowTargetModel>(
      `WorkflowTarget`,
      {},
      false,
    );
    return response;
  }

  public async postWorkflowTarget(
    model: WorkflowTargetModel,
  ): Promise<WorkflowTargetModel> {
    // -- not used
    const response = await this.http.post<WorkflowTargetModel>(
      `WorkflowTarget`,
      model,
    );
    return response;
  }

  public async getWorkflowTargetById(id: number): Promise<WorkflowTargetModel> {
    // -- not used
    const response = await this.http.get<WorkflowTargetModel>(
      `WorkflowTarget/${id}`,
      { id: id },
      false,
    );
    return response;
  }
}
