/* eslint-disable prefer-const */

import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { RequestWithUser } from 'apps/auth-api/src/auth/jwt-payload.interface';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { FaultsModel } from '../providers/models/quotationheader/faults.model';
import { TicketAreaModel } from '../providers/models/ticketing/ticketArea.model';
import { TicketingService } from '../providers/services/ticketing.service';
@Controller()
export class TicketingController {
  constructor(private readonly service: TicketingService) {}

  @Get('tickets')
  // @Auth()
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTickets(
    @Query() query?: any,
    @Req() req?: RequestWithUser,
  ): Promise<any[]> {
    console.log(req?.headers);
    // -- not used
    let {
      advanced_search,
      agent,
      agent_id,
      alerttype,
      asset_id,
      awaitinginput,
      billableonly,
      billing_date,
      billing_type,
      billingcontractid,
      calendar_enddate,
      calendar_startdate,
      category_1,
      category_2,
      category_3,
      category_4,
      checkmyticketsonly,
      client_id,
      client_ids,
      client_ref,
      closed_only,
      columns_id,
      contract_id,
      contract_period,
      count,
      datesearch,
      debug,
      default_columns,
      deleted,
      domain,
      enddate,
      enddatetime,
      excludeslacalcs,
      excludethese,
      excludetickettypeallowall,
      extraportalfilter,
      facebook_id,
      fetchgrandchildren,
      flagged,
      followedandagents,
      ignoremilestonerestriction,
      includeaccountmanager,
      includeagent,
      includeallopen,
      includeappointmentid,
      includeapproval,
      includeassetkeyfield,
      includeassettype,
      includebreached,
      includebudgettype,
      includechildids,
      includechildread,
      includechildren,
      includeclosed,
      includecolumns,
      includecompleted,
      includecontract,
      includecountryregion,
      includefirstname,
      include_custom_fields,
      includefollowedonly,
      includehold,
      includeinactivetechs,
      includeinactiveusers,
      includeitilname,
      includelastaction,
      includelastincomingemail,
      includelastname,
      includelastnote,
      includelocked,
      includemailbox,
      includemailid,
      includemyuseronly,
      includenextactivitydate,
      includenextappointmenttype,
      includeparentsubject,
      includeprojects,
      includeread,
      includerelatedservices,
      includerelease1,
      includerelease2,
      includerelease3,
      includeservicecategory,
      includeslaactiondate,
      includeslatimer,
      includestatus,
      includesubmittedonly,
      includesupplier,
      includetickettype,
      includetimetaken,
      includetoplevel,
      includeviewing,
      includeworkflowstage,
      includeworkflowstagenumber,
      includuserdepartments,
      inlcludeopenchildcount,
      invlucebranch,
      ismilestone,
      isorion,
      isquicktimesearch,
      isscom,
      isteams,
      iszapier,
      itil_requesttype,
      itil_requesttype_id,
      kanbanviewontheagentapp,
      kanbanviewontheportal,
      lastupdatefromdate,
      lastupdatetodate,
      list_id,
      milestone_id,
      mine,
      nochargeonly,
      notime,
      onlytime,
      open_only,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      orion_type,
      page_no,
      page_size,
      pageinate,
      parent_id,
      pending_review,
      per_action,
      prepayorcontractonly,
      priority,
      product,
      project_ids,
      ready_for_invoicing,
      related_id,
      release_id,
      requesttype,
      requesttype_id,
      requesttypegroup,
      search,
      search_details,
      search_id,
      search_inventory_number,
      search_oppcompanyname,
      search_oppcontactname,
      search_oppemailaddress,
      search_release1,
      search_release2,
      search_release3,
      search_releasenote,
      search_reportedby,
      search_summary,
      search_supplier_reference,
      search_user_name,
      search_version,
      searchactions,
      searchthisticketid,
      service_id,
      showonroadmap,
      third_party_id,
      third_party_id_string,
      site_id,
      sitepostcode,
      sla,
      sprint_for_tickettype_id,
      sprints,
      startandendset,
      startdate,
      startdatetime,
      status,
      status_id,
      submittedandagents,
      supplier_id,
      supplier_status,
      team,
      team_name,
      ticketarea_id,
      ticketcontract_id,
      ticketidonly,
      ticketids,
      ticketlinktype,
      toplevel_id,
      unlinked_only,
      user_id,
      username,
      utcoffset,
      view_id,
      withattachments,
    } = query;

    user_id = req?.user?.halloProfile?.PersonId;
    const response = await this.service.getTickets(
      advanced_search,
      agent,
      agent_id,
      alerttype,
      asset_id,
      awaitinginput,
      billableonly,
      billing_date,
      billing_type,
      billingcontractid,
      calendar_enddate,
      calendar_startdate,
      category_1,
      category_2,
      category_3,
      category_4,
      checkmyticketsonly,
      client_id,
      client_ids,
      client_ref,
      closed_only,
      columns_id,
      contract_id,
      contract_period,
      count,
      datesearch,
      debug,
      default_columns,
      deleted,
      domain,
      enddate,
      enddatetime,
      excludeslacalcs,
      excludethese,
      excludetickettypeallowall,
      extraportalfilter,
      facebook_id,
      fetchgrandchildren,
      flagged,
      followedandagents,
      ignoremilestonerestriction,
      includeaccountmanager,
      includeagent,
      includeallopen,
      includeappointmentid,
      includeapproval,
      includeassetkeyfield,
      includeassettype,
      includebreached,
      includebudgettype,
      includechildids,
      includechildread,
      includechildren,
      includeclosed,
      includecolumns,
      includecompleted,
      includecontract,
      includecountryregion,
      includefirstname,
      include_custom_fields,
      includefollowedonly,
      includehold,
      includeinactivetechs,
      includeinactiveusers,
      includeitilname,
      includelastaction,
      includelastincomingemail,
      includelastname,
      includelastnote,
      includelocked,
      includemailbox,
      includemailid,
      includemyuseronly,
      includenextactivitydate,
      includenextappointmenttype,
      includeparentsubject,
      includeprojects,
      includeread,
      includerelatedservices,
      includerelease1,
      includerelease2,
      includerelease3,
      includeservicecategory,
      includeslaactiondate,
      includeslatimer,
      includestatus,
      includesubmittedonly,
      includesupplier,
      includetickettype,
      includetimetaken,
      includetoplevel,
      includeviewing,
      includeworkflowstage,
      includeworkflowstagenumber,
      includuserdepartments,
      inlcludeopenchildcount,
      invlucebranch,
      ismilestone,
      isorion,
      isquicktimesearch,
      isscom,
      isteams,
      iszapier,
      itil_requesttype,
      itil_requesttype_id,
      kanbanviewontheagentapp,
      kanbanviewontheportal,
      lastupdatefromdate,
      lastupdatetodate,
      list_id,
      milestone_id,
      mine,
      nochargeonly,
      notime,
      onlytime,
      open_only,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      orion_type,
      page_no,
      page_size,
      pageinate,
      parent_id,
      pending_review,
      per_action,
      prepayorcontractonly,
      priority,
      product,
      project_ids,
      ready_for_invoicing,
      related_id,
      release_id,
      requesttype,
      requesttype_id,
      requesttypegroup,
      search,
      search_details,
      search_id,
      search_inventory_number,
      search_oppcompanyname,
      search_oppcontactname,
      search_oppemailaddress,
      search_release1,
      search_release2,
      search_release3,
      search_releasenote,
      search_reportedby,
      search_summary,
      search_supplier_reference,
      search_user_name,
      search_version,
      searchactions,
      searchthisticketid,
      service_id,
      showonroadmap,
      third_party_id,
      third_party_id_string,
      site_id,
      sitepostcode,
      sla,
      sprint_for_tickettype_id,
      sprints,
      startandendset,
      startdate,
      startdatetime,
      status,
      status_id,
      submittedandagents,
      supplier_id,
      supplier_status,
      team,
      team_name,
      ticketarea_id,
      ticketcontract_id,
      ticketidonly,
      ticketids,
      ticketlinktype,
      toplevel_id,
      unlinked_only,
      user_id,
      username,
      utcoffset,
      view_id,
      withattachments,
    );
    return response;
  }

  @Post('tickets')
  @ApiBody({ type: FaultsModel })
  @ApiResponse({ type: FaultsModel })
  public async postTickets(
    @Body() body: FaultsModel,
    @Req() req: any,
  ): Promise<FaultsModel> {
    // -- not used
    body.user_id = req?.user?.halloProfile?.PersonId;
    const response = await this.service.postTickets(body);
    return response;
  }

  @Get('tickets/:id')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTicketsById(
    @Query() query?: any,
    @Param('id') id?: string,
  ): Promise<any> {
    // -- not used
    const {
      // id,
      amailentryid,
      assignedto,
      consignablelines,
      debug,
      dodatabaselookup,
      email,
      include_auditing,
      includeagent,
      includechildids,
      includedetails,
      includelastaction,
      includelastappointment,
      includelinkedobjects,
      includenextappointment,
      includeparentchangeinfo,
      includeparentsubject,
      includeseenby,
      is_portal,
      isdetailscreen,
      ishalolink,
      ispreview,
      isteams,
      nocache,
      subject,
      ticketidonly,
      utcoffset,
    } = query;
    const response = await this.service.getTicketsById(
      id,
      amailentryid,
      assignedto,
      consignablelines,
      debug,
      dodatabaselookup,
      email,
      include_auditing,
      includeagent,
      includechildids,
      includedetails,
      includelastaction,
      includelastappointment,
      includelinkedobjects,
      includenextappointment,
      includeparentchangeinfo,
      includeparentsubject,
      includeseenby,
      is_portal,
      isdetailscreen,
      ishalolink,
      ispreview,
      isteams,
      nocache,
      subject,
      ticketidonly,
      utcoffset,
    );
    return response;
  }

  @Post('tickets/object')
  @ApiBody({ type: FaultsModel })
  @ApiResponse({ type: FaultsModel })
  public async postTicketsObject(
    @Body() body: FaultsModel,
  ): Promise<FaultsModel> {
    // -- not used
    const response = await this.service.postTicketsObject(body);
    return response;
  }

  @Post('tickets/vote')
  @ApiBody({ type: FaultsModel })
  @ApiResponse({ type: FaultsModel })
  public async postTicketsVote(
    @Body() body: FaultsModel,
  ): Promise<FaultsModel> {
    // -- not used
    const response = await this.service.postTicketsVote(body);
    return response;
  }

  @Post('tickets/view')
  @ApiBody({ type: FaultsModel })
  @ApiResponse({ type: FaultsModel })
  public async postTicketsView(
    @Body() body: FaultsModel,
  ): Promise<FaultsModel> {
    // -- not used
    const response = await this.service.postTicketsView(body);
    return response;
  }

  @Post('tickets/processchildren')
  @ApiBody({ type: FaultsModel })
  @ApiResponse({ type: FaultsModel })
  public async postTicketsProcessChildren(
    @Body() body: FaultsModel,
  ): Promise<FaultsModel> {
    // -- not used
    const response = await this.service.postTicketsProcessChildren(body);
    return response;
  }

  @Get('ticketArea')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TicketAreaModel })
  public async getTicketArea(@Query() query?: any): Promise<TicketAreaModel> {
    // -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.getTicketArea();
    return response;
  }

  @Post('ticketArea')
  @ApiBody({ type: TicketAreaModel })
  @ApiResponse({ type: TicketAreaModel })
  public async postTicketArea(
    @Body() body: TicketAreaModel,
  ): Promise<TicketAreaModel> {
    // -- not used
    const response = await this.service.postTicketArea(body);
    return response;
  }

  @Get('ticketArea/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TicketAreaModel })
  public async getTicketAreaById(
    @Query() query?: any,
  ): Promise<TicketAreaModel> {
    // -- not used
    const { id, includedetails } = query;
    const response = await this.service.getTicketAreaById(id, includedetails);
    return response;
  }
}
