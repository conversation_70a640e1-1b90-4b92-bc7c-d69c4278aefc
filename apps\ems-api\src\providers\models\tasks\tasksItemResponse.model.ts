import { LinkModel } from '../talent/link.model';
import { TasksChangedAttributesItemResponseModel } from '../tasks/tasksChangedAttributesItemResponse.model';
// Tasks-item-response
export class TasksItemResponseModel {
  // -- not used
  public AdditionalInformation?: string;
  public links: LinkModel[];
  public assignees: any;
  public IdentificationKey?: number;
  public ModuleIdentifier?: string;
  public changedAttributes: TasksChangedAttributesItemResponseModel[];
  public taskDefinitionName: string;
  public titlePrefix: string;
  public createdDate?: string;
  public createdBy: string;
  public title: string;
  public category: string;
  public assignedDate?: string;
  public fromUserName?: string;
  public state: string;
}
