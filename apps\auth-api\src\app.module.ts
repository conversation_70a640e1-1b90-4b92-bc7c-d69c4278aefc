import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from 'packages/http';
import { ExternalAuthInterceptor } from 'packages/http/externalauth.interceptor';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { EtisalatAuthModule } from './auth/ethisalat.module';
import { TokenRefreshMiddleware } from './auth/middleware/token-refresh.middleware';
import { ConfigModule as CustomConfigModule } from './config/config.module';
import { configService } from './config/config.service';
import { Token } from './entities/token.entity';
import { User } from './entities/user.entity';
import { UserProfile } from './entities/userProfile.entity';
import { SyncAccountsService } from './schedulers/syncScheduler';

const typeOrmSettings = Object.assign(configService.getTypeOrmConfig(), {
  autoLoadEntities: true,
  entities: [User, Token, UserProfile],
});
@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmSettings),
    TypeOrmModule.forFeature([User, UserProfile]),
    AuthModule,
    EtisalatAuthModule,
    HttpModule.register({}),
    CustomConfigModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        process.env.NODE_ENV === 'production'
          ? '.env.production'
          : '.env.development',
    }),
    ScheduleModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    SyncAccountsService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ExternalAuthInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TokenRefreshMiddleware)
      .exclude(
        // Exclude auth routes to avoid circular calls
        { path: '/auth/login', method: RequestMethod.ALL },
        { path: '/auth/callback', method: RequestMethod.ALL },
        { path: '/auth/refresh', method: RequestMethod.ALL },
        { path: '/auth/access-token', method: RequestMethod.ALL },
        // Exclude health check or other public routes
        { path: '/health', method: RequestMethod.ALL },
        { path: '/', method: RequestMethod.GET },
      )
      .forRoutes('*'); // Apply to all other routes
  }
}
