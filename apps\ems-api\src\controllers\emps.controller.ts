import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { EmpsModel } from '../providers/models/emps/emps.model';
import { EmpsAssignmentsAssignmentDFFItemPostRequestModel } from '../providers/models/emps/empsAssignmentsAssignmentDFFItemPostRequest.model';
import { EmpsAssignmentsAssignmentDFFItemResponseModel } from '../providers/models/emps/empsAssignmentsAssignmentDFFItemResponse.model';
import { EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel } from '../providers/models/emps/empsAssignmentsAssignmentExtraInformationItemPostRequest.model';
import { EmpsAssignmentsAssignmentExtraInformationItemResponseModel } from '../providers/models/emps/empsAssignmentsAssignmentExtraInformationItemResponse.model';
import { EmpsAssignmentsEmprepsItemResponseModel } from '../providers/models/emps/empsAssignmentsEmprepsItemResponse.model';
import { EmpsAssignmentsItemPostRequestModel } from '../providers/models/emps/empsAssignmentsItemPostRequest.model';
import { EmpsAssignmentsItemResponseModel } from '../providers/models/emps/empsAssignmentsItemResponse.model';
import { EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel } from '../providers/models/emps/empsAssignmentsPeopleGroupKeyFlexfieldItemResponse.model';
import { EmpsDirectReportsItemResponseModel } from '../providers/models/emps/empsDirectReportsItemResponse.model';
import { EmpsItemPostRequestModel } from '../providers/models/emps/empsItemPostRequest.model';
import { EmpsItemResponseModel } from '../providers/models/emps/empsItemResponse.model';
import { EmpsPersonDFFItemPostRequestModel } from '../providers/models/emps/empsPersonDFFItemPostRequest.model';
import { EmpsPersonDFFItemResponseModel } from '../providers/models/emps/empsPersonDFFItemResponse.model';
import { EmpsPersonExtraInformationItemPostRequestModel } from '../providers/models/emps/empsPersonExtraInformationItemPostRequest.model';
import { EmpsPersonExtraInformationItemResponseModel } from '../providers/models/emps/empsPersonExtraInformationItemResponse.model';
import { EmpsPhotoItemPostRequestModel } from '../providers/models/emps/empsPhotoItemPostRequest.model';
import { EmpsPhotoItemResponseModel } from '../providers/models/emps/empsPhotoItemResponse.model';
import { EmpsRolesItemPostRequestModel } from '../providers/models/emps/empsRolesItemPostRequest.model';
import { EmpsRolesItemResponseModel } from '../providers/models/emps/empsRolesItemResponse.model';
import { EmpsVisasItemResponseModel } from '../providers/models/emps/empsVisasItemResponse.model';
import { EmpsService } from '../providers/services/emps.service';

import { RequestWithUser } from 'apps/auth-api/src/auth/jwt-payload.interface';
import { fileTypeFromBuffer } from 'file-type';
import { EmpsEmployerResponseModel } from '../providers/models/emps/empsEmployee.model';
import { GradeIdLOVItemModel } from '../providers/models/emps/empsGradeLOV.model';
@Controller()
export class EmpsController {
  constructor(private readonly service: EmpsService) {}
  @Get(
    'emps/:empsUniqID/child/assignments/:assignmentsUniqID/child/assignmentExtraInformation/:assignmentExtraInformationUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: EmpsAssignmentsAssignmentExtraInformationItemResponseModel,
  })
  public async getEmpsAssignmentsExtraInfo(
    @Query() query?: any,
  ): Promise<EmpsAssignmentsAssignmentExtraInformationItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      assignmentsUniqID,
      assignmentExtraInformationUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getEmpsAssignmentsExtraInfo(
      empsUniqID,
      assignmentsUniqID,
      assignmentExtraInformationUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }
  @Post('emps/:empsUniqID/child/roles')
  @ApiBody({ type: EmpsRolesItemPostRequestModel })
  @ApiResponse({ type: EmpsRolesItemPostRequestModel })
  public async postEmpsRoles(
    @Body() body: EmpsRolesItemPostRequestModel,
  ): Promise<EmpsRolesItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsRoles(body);
    return response;
  }

  @Get('emps/:id/child/photo/:imageId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsPhotoItemResponseModel })
  public async getEmpsPhoto(
    @Param() params: any,
    @Query() query?: any,
  ): Promise<EmpsPhotoItemResponseModel> {
    const { id: empsUniqID, imageId } = params;
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getEmpsPhoto(
      empsUniqID,
      imageId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('emps/:uid/child/assignments/:aid/child/assignmentDFF')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsAssignmentsAssignmentDFFItemResponseModel })
  public async getEmpsAssignmentDFF(
    @Req() req?: RequestWithUser,
  @Query() query?: any,
): Promise<GradeIdLOVItemModel> {
  if (!query) {
    query = {};
  }

  // Extract the UniqueIds from the user's fusionProfile
  const assignmentsUniqID = req?.user?.fusionProfile?.AssignmentUniqueId;
  const empsUniqID = req?.user?.fusionProfile?.UniqueId;
  if (!empsUniqID) {
    throw new UnauthorizedException('User UniqueId not found in profile');
  }
const { expand, fields, onlyData, dependency, links } = query;

  const response = await this.service. getEmpsAssignmentDFF(
    empsUniqID,          // Pass the employee UniqueId
    assignmentsUniqID??"",   // Pass the assignment UniqueId
    expand,
    fields,
    onlyData,
    dependency,
    links,
  );
  return response;
}

 @Get('emps/:empsUniqID/child/assignments/:assignmentsUniqID/lov/GradeIdLOV')
@UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
@ApiResponse({ type: GradeIdLOVItemModel })
public async getLovGrade(
  @Req() req?: RequestWithUser,
  @Query() query?: any,
): Promise<GradeIdLOVItemModel> {
  if (!query) {
    query = {};
  }

  // Extract the UniqueIds from the user's fusionProfile
  const assignmentsUniqID = req?.user?.fusionProfile?.AssignmentUniqueId;
  const empsUniqID = req?.user?.fusionProfile?.UniqueId;
  if (!empsUniqID) {
    throw new UnauthorizedException('User UniqueId not found in profile');
  }

  const { expand, fields, onlyData, dependency, links } = query;

  const response = await this.service.getLovGrade(
    empsUniqID,          // Pass the employee UniqueId
    assignmentsUniqID??"",   // Pass the assignment UniqueId
    expand,
    fields,
    onlyData,
    dependency,
    links,
  );
  return response;
}

  @Post('emps/:empsUniqID/child/personDFF')
  @ApiBody({ type: EmpsPersonDFFItemPostRequestModel })
  @ApiResponse({ type: EmpsPersonDFFItemPostRequestModel })
  public async postEmpsPersonDFF(
    @Body() body: EmpsPersonDFFItemPostRequestModel,
  ): Promise<EmpsPersonDFFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsPersonDFF(body);
    return response;
  }
  @Get(
    'emps/:empsUniqID/child/personExtraInformation/:personExtraInformationUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsPersonExtraInformationItemResponseModel })
  public async getEmpsPersonExtraInfo(
    @Query() query?: any,
  ): Promise<EmpsPersonExtraInformationItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      personExtraInformationUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getEmpsPersonExtraInfo(
      empsUniqID,
      personExtraInformationUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
  @Get('emps/:empsUniqID/child/assignments')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsAssignmentsItemResponseModel })
  public async getEmpsAssignments(
    @Req() req?: RequestWithUser,
    @Query() query?: any,
  ): Promise<EmpsAssignmentsItemResponseModel> {
    if (!query) {
      query = {};
    }

    // Extract the UniqueId from the user's fusionProfile
    const empsUniqID = req?.user?.fusionProfile?.UniqueId;
    if (!empsUniqID) {
      throw new UnauthorizedException('User UniqueId not found in profile');
    }

    const { expand, fields, onlyData, dependency, links, effectiveOf } = query;

    const response = await this.service.getEmpsAssignments(
      empsUniqID, // Pass the extracted UniqueId as the first parameter
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  // ..........
  @Get('emps/:UniqueId/lov/LegalEmployerLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsEmployerResponseModel })
  public async getEmployerLOV(
    @Req() req?: RequestWithUser,
    @Query() query?: any,
  ): Promise<EmpsEmployerResponseModel> {
    if (!query) {
      query = {};
    }

    // Extract the UniqueId from the user's fusionProfile
    const empsUniqID = req?.user?.fusionProfile?.UniqueId;
    if (!empsUniqID) {
      throw new UnauthorizedException('User UniqueId not found in profile');
    }

    const { expand, fields, onlyData, dependency, links, effectiveOf } = query;

    const response = await this.service.getEmployerLOV(
      empsUniqID, // Pass the extracted UniqueId as the first parameter
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  @Get('emps')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<EmpsModel> })
  public async getEmps_2(
    @Query() query?: any,
    @Req() req?: RequestWithUser,
  ): Promise<EmpsModel[]> {
    if (!query) {
      query = {};
    }

    let { q } = query;
    q = `PersonId=${req?.user?.fusionProfile?.PersonId};` + (q ? q : '');
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    } = query;

    const response = await this.service.getEmps_2(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    );
    return response;
  }
  @Get('emps/all')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<EmpsModel> })
  public async getAllEmps(@Query() query?: any): Promise<EmpsModel[]> {
    if (!query) {
      query = {};
    }
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    } = query;

    const response = await this.service.getEmps_2(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    );
    return response;
  }
  @Post('emps')
  @ApiBody({ type: EmpsItemPostRequestModel })
  @ApiResponse({ type: EmpsItemPostRequestModel })
  public async postEmps_2(
    @Body() body: EmpsItemPostRequestModel,
  ): Promise<EmpsItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmps_2(body);
    return response;
  }
  @Post(
    'emps/:empsUniqID/child/assignments/:assignmentsUniqID/child/assignmentExtraInformation',
  )
  @ApiBody({
    type: EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel,
  })
  @ApiResponse({
    type: EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel,
  })
  public async postEmpsAssignmentsExtraInfoList(
    @Body() body: EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel,
  ): Promise<EmpsAssignmentsAssignmentExtraInformationItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsAssignmentsExtraInfoList(body);
    return response;
  }
  @Get(
    'emps/:empsUniqID/child/assignments/:assignmentsUniqID/child/peopleGroupKeyFlexfield/:_PEOPLE_GROUP_ID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel,
  })
  public async getEmpsAssignmentsPeopleGroupKFById(
    @Query() query?: any,
  ): Promise<EmpsAssignmentsPeopleGroupKeyFlexfieldItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      assignmentsUniqID,
      _PEOPLE_GROUP_ID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getEmpsAssignmentsPeopleGroupKFById(
      empsUniqID,
      assignmentsUniqID,
      _PEOPLE_GROUP_ID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
  @Post('emps/:empsUniqID/child/personExtraInformation')
  @ApiBody({ type: EmpsPersonExtraInformationItemPostRequestModel })
  @ApiResponse({ type: EmpsPersonExtraInformationItemPostRequestModel })
  public async postEmpsPersonExtraInfoList(
    @Body() body: EmpsPersonExtraInformationItemPostRequestModel,
  ): Promise<EmpsPersonExtraInformationItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsPersonExtraInfoList(body);
    return response;
  }
  @Get('emps/:empsUniqID/child/personDFF/:personId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsPersonDFFItemResponseModel })
  public async getEmpsPersonDFFById(
    @Query() query?: any,
  ): Promise<EmpsPersonDFFItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      personId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getEmpsPersonDFFById(
      empsUniqID,
      personId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
  @Get('emps/:id/child/roles')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsRolesItemResponseModel })
  public async getEmpsRolesById(
    @Param() params: any,
    @Query() query?: any,
  ): Promise<EmpsRolesItemResponseModel> {
    const { id: empsUniqID } = params; // Extract query parameters
    const { expand, fields, onlyData, dependency, links, effectiveOf } = query; // Call the service method with proper parameters
    const response = await this.service.getEmpsRolesById(
      empsUniqID ?? '',
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  @Get('emps/:empsUniqID/child/visas/:visasUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsVisasItemResponseModel })
  public async getEmpsVisas(
    @Query() query?: any,
  ): Promise<EmpsVisasItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      visasUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getEmpsVisas(
      empsUniqID,
      visasUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }
  @Get(
    'emps/:empsUniqID/child/assignments/:assignmentsUniqID/child/empreps/:emprepsUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsAssignmentsEmprepsItemResponseModel })
  public async getEmpsAssignmentsEmpRepsById(
    @Query() query?: any,
  ): Promise<EmpsAssignmentsEmprepsItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      assignmentsUniqID,
      emprepsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getEmpsAssignmentsEmpRepsById(
      empsUniqID,
      assignmentsUniqID,
      emprepsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }
  @Post(
    'emps/:empsUniqID/child/assignments/:assignmentsUniqID/child/assignmentDFF',
  )
  @ApiBody({ type: EmpsAssignmentsAssignmentDFFItemPostRequestModel })
  @ApiResponse({ type: EmpsAssignmentsAssignmentDFFItemPostRequestModel })
  public async postEmpsAssignmentsDFF(
    @Body() body: EmpsAssignmentsAssignmentDFFItemPostRequestModel,
  ): Promise<EmpsAssignmentsAssignmentDFFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsAssignmentsDFF(body);
    return response;
  }
  @Post('emps/:empsUniqID/child/assignments')
  @ApiBody({ type: EmpsAssignmentsItemPostRequestModel })
  @ApiResponse({ type: EmpsAssignmentsItemPostRequestModel })
  public async postEmpsAssignmentsList(
    @Body() body: EmpsAssignmentsItemPostRequestModel,
  ): Promise<EmpsAssignmentsItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsAssignmentsList(body);
    return response;
  }
  @Get('emps/:empsUniqID/child/directReports/:directReportsUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsDirectReportsItemResponseModel })
  public async getEmpsDirectReportsById(
    @Query() query?: any,
  ): Promise<EmpsDirectReportsItemResponseModel> {
    // -- not used
    const {
      empsUniqID,
      directReportsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getEmpsDirectReportsById(
      empsUniqID,
      directReportsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  //   @Get('emps/:empsUniqID/child/photo/:imageId/enclosure/image')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  // @ApiResponse({ type: EmpsPhotoItemResponseModel })
  // public async getEmpsPhotoImage(
  //   @Param() params: any,
  //   @Query() query?: any,
  // ): Promise<EmpsPhotoItemResponseModel> {
  //   const { empsUniqID, imageId } = params;
  //   const response = await this.service.getEmpsPhotoImage(empsUniqID, imageId);
  //   return response;
  // }
  @Get('emps/:empsUniqID/child/photo/:imageId/enclosure/image')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsPhotoItemResponseModel })
  public async getEmpsPhotoImage(
    @Param() params: any,
     @Req() req?: any,
    @Res() res?: any,
  ): Promise<void> {
     const empsUniqID = req.user.fusionProfile?.UniqueId;
      const imageId = req.user.fusionProfile?.FusionImageId;
    res.setHeader('Content-Disposition', `inline; filename="${imageId}.jpg"`);
    const imageBuffer: ArrayBuffer = await this.service.getEmpsPhotoImage(
      empsUniqID,
      imageId,
    );

    if (!imageBuffer) {
      return res.status(404).send('Image not found');
    }

    // Detect file type from buffer
    const detected = await fileTypeFromBuffer(imageBuffer);
    const contentType = detected?.mime || 'application/octet-stream';

    // Set content-type for browser display
    res.setHeader('Content-Type', contentType);

    // Send the binary image
    res.end(imageBuffer);
  }
  @Put('emps/:empsUniqID/child/photo/:imageId/enclosure/image')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiBody({ type: EmpsPhotoItemResponseModel })
  @ApiResponse({ type: EmpsPhotoItemResponseModel })
  public async putEmpsPhotoImage(
    @Param() params: any,
    @Body() body: EmpsPhotoItemResponseModel,
  ): Promise<EmpsPhotoItemResponseModel> {
    const { empsUniqID, imageId } = params;
    const response = await this.service.putEmpsPhotoImage(
      empsUniqID,
      imageId,
      body,
    );
    return response;
  }
  @Get('emps/:empsUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsItemResponseModel })
  public async getEmps(
    @Req() req?: RequestWithUser,
    @Query() query?: any,
  ): Promise<EmpsItemResponseModel> {
     if (!query) {
      query = {};
    }

    // Extract the UniqueId from the user's fusionProfile
    const empsUniqID = req?.user?.fusionProfile?.UniqueId;
    if (!empsUniqID) {
      throw new UnauthorizedException('User UniqueId not found in profile');
    }
    const { expand, fields, onlyData, dependency, links, effectiveOf } = query;
    const response = await this.service.getEmps(
      empsUniqID ?? '',
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }
  @Post('emps/:empsUniqID/child/photo')
  @ApiBody({ type: EmpsPhotoItemPostRequestModel })
  @ApiResponse({ type: EmpsPhotoItemPostRequestModel })
  public async postEmpsPhotoList(
    @Body() body: EmpsPhotoItemPostRequestModel,
  ): Promise<EmpsPhotoItemPostRequestModel> {
    // -- not used
    const response = await this.service.postEmpsPhotoList(body);
    return response;
  }

  @Patch('emps/:id/child/photo/:imageId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: EmpsPhotoItemResponseModel })
  public async updateEmpsPhoto(
    @Param() params: any,
    @Body() body: any,
    @Query() query?: any,
  ): Promise<EmpsPhotoItemResponseModel> {
    const { id: empsUniqID, imageId } = params;

    const cleanBody = {
      Image: body.Image,
      ImageName: body.ImageName,
      PrimaryFlag: body.PrimaryFlag,
      ObjectVersionNumber: body.ObjectVersionNumber,
    };

    return await this.service.updateEmpsPhoto(
      empsUniqID,
      imageId,
      cleanBody,
      query,
    );
  }
}
