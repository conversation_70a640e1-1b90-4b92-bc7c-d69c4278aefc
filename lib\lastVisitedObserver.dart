import 'package:flutter/material.dart';
import 'package:seawork/data/preferencesUtils.dart';

class LastVisitedObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _storeVisited(route);
    super.didPush(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    if (newRoute != null) {
      _storeVisited(newRoute);
    }
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  // No need to store on didPop
  // because it causes overwriting with the popped-to route

  void _storeVisited(Route<dynamic> route) {
    final uri = route.settings.name ?? route.settings.arguments?.toString();

    if (uri != null &&
        uri != '/login' &&
        uri != '/oauthredirect' &&
        uri != '/') {
      PreferencesUtils.setLastVisitedPage(uri);
      debugPrint('Stored last visited page: $uri');
    }
  }
}
