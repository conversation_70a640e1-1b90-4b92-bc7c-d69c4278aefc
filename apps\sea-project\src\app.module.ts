import { Module } from '@nestjs/common';
import { AppModule as AuthAppModule } from 'apps/auth-api/src/app.module';
import { AppModule as EmsAppModule } from 'apps/ems-api/src/app.module';
import { AppModule as NmsAppModule } from 'apps/nms-api/src/app.module';
import { AppModule as PmsAppModule } from 'apps/pms-api/src/app.module';
import { AppModule as SsrAppModule } from 'apps/ssr-api/src/app.module';

@Module({
  imports: [
    EmsAppModule,
    SsrAppModule,
    AuthAppModule,
    NmsAppModule,
    PmsAppModule,
  ],
  controllers: [],
  providers: [],
})
export class AllAppModule {}
