/* eslint-disable prefer-const */

import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { FormattedEmailModel } from '../providers/models/formattedemail/formattedEmail.model';
import { ModuleSetupModel } from '../providers/models/modulesetup/moduleSetup.model';
import { NHD_DeviceInfoModel } from '../providers/models/nhd/nHD_DeviceInfo.model';
import { FaultApprovalModel } from '../providers/models/quotationheader/faultApproval.model';
import { ReleaseProductModel } from '../providers/models/releaseproduct/releaseProduct.model';
import { ServStatusSubscribeModel } from '../providers/models/servstatus/servStatusSubscribe.model';
import { AddressStoreModel } from '../providers/models/workflowtarget/addressStore.model';
import { AreaAzureTenantModel } from '../providers/models/workflowtarget/areaAzureTenant.model';
import { AreaToDoModel } from '../providers/models/workflowtarget/areaToDo.model';
import { AttachmentModel } from '../providers/models/workflowtarget/attachment.model';
import { AuditModel } from '../providers/models/workflowtarget/audit.model';
import { AutoassignModel } from '../providers/models/workflowtarget/autoassign.model';
import { AzureADConnectionModel } from '../providers/models/workflowtarget/azureADConnection.model';
import { AzureADMappingModel } from '../providers/models/workflowtarget/azureADMapping.model';
import { CurrencyModel } from '../providers/models/workflowtarget/currency.model';
import { CustomTableModel } from '../providers/models/workflowtarget/customTable.model';
import { DbTypeModel } from '../providers/models/workflowtarget/dbType.model';
import { DeviceModel } from '../providers/models/workflowtarget/device.model';
import { ExternalLink_ListModel } from '../providers/models/workflowtarget/externalLink_List.model';
import { FAQListHeadModel } from '../providers/models/workflowtarget/fAQListHead.model';
import { FieldGroupModel } from '../providers/models/workflowtarget/fieldGroup.model';
import { FieldInfoModel } from '../providers/models/workflowtarget/fieldInfo.model';
import { IntegrationFieldMappingModel } from '../providers/models/workflowtarget/integrationFieldMapping.model';
import { InvoiceChangeModel } from '../providers/models/workflowtarget/invoiceChange.model';
import { InvoiceDetailModel } from '../providers/models/workflowtarget/invoiceDetail.model';
import { InvoiceDetailProRataModel } from '../providers/models/workflowtarget/invoiceDetailProRata.model';
import { InvoiceHeaderModel } from '../providers/models/workflowtarget/invoiceHeader.model';
import { InvoicePayment_ListModel } from '../providers/models/workflowtarget/invoicePayment_List.model';
import { ItemModel } from '../providers/models/workflowtarget/item.model';
import { ItemStockModel } from '../providers/models/workflowtarget/itemStock.model';
import { ItemSupplierModel } from '../providers/models/workflowtarget/itemSupplier.model';
import { LansweeperSoftwareModel } from '../providers/models/workflowtarget/lansweeperSoftware.model';
import { Licence_ListModel } from '../providers/models/workflowtarget/licence_List.model';
import { LicenceRoleModel } from '../providers/models/workflowtarget/licenceRole.model';
import { MarketingUnsubscribeModel } from '../providers/models/workflowtarget/marketingUnsubscribe.model';
import { PartsLookupModel } from '../providers/models/workflowtarget/partsLookup.model';
import { QualysHostAssetSoftwareHostAssetSoftwareModel } from '../providers/models/workflowtarget/qualysHostAssetSoftwareHostAssetSoftware.model';
import { QuickBooksDetailsModel } from '../providers/models/workflowtarget/quickBooksDetails.model';
import { RequestTypeFieldModel } from '../providers/models/workflowtarget/requestTypeField.model';
import { ServiceRequestDetailsModel } from '../providers/models/workflowtarget/serviceRequestDetails.model';
import { ServiceRestrictionModel } from '../providers/models/workflowtarget/serviceRestriction.model';
import { ServiceUserModel } from '../providers/models/workflowtarget/serviceUser.model';
import { ServSiteModel } from '../providers/models/workflowtarget/servSite.model';
import { ServStatusModel } from '../providers/models/workflowtarget/servStatus.model';
import { TagModel } from '../providers/models/workflowtarget/tag.model';
import { WorkflowTargetModel } from '../providers/models/workflowtarget/workflowTarget.model';
import { WorkflowtargetService } from '../providers/services/workflowtarget.service';
@Controller()
export class WorkflowtargetController {
  constructor(private readonly service: WorkflowtargetService) {}

  @Get('address')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AddressStoreModel> })
  public async getAddresses(
    @Query() query?: any,
  ): Promise<AddressStoreModel[]> {
    // -- not used
    let {
      count,
      postcode,
      site_id,
      type_id,
      user_id,
      openedafter,
      onholdonly,
      overrideclientid,
      overridesiteid,
      overrideuserid,
    } = query;
    let response = await this.service.getAddresses(
      count,
      postcode,
      site_id,
      type_id,
      user_id,
      openedafter,
      onholdonly,
      overrideclientid,
      overridesiteid,
      overrideuserid,
    );
    return response;
  }

  @Post('address')
  @ApiBody({ type: AddressStoreModel })
  @ApiResponse({ type: AddressStoreModel })
  public async postAddresses(
    @Body() body: AddressStoreModel,
  ): Promise<AddressStoreModel> {
    // -- not used
    let response = await this.service.postAddresses(body);
    return response;
  }

  @Get('address/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AddressStoreModel })
  public async getAddressById(
    @Query() query?: any,
  ): Promise<AddressStoreModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getAddressById(id, includedetails);
    return response;
  }

  @Get('alemba/get')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AreaToDoModel> })
  public async getAlembaData(@Query() query?: any): Promise<AreaToDoModel[]> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getAlembaData();
    return response;
  }

  @Post('approvalStore')
  @ApiBody({ type: AreaAzureTenantModel })
  @ApiResponse({ type: AreaAzureTenantModel })
  public async postApprovalStoreData(
    @Body() body: AreaAzureTenantModel,
  ): Promise<AreaAzureTenantModel> {
    // -- not used
    let response = await this.service.postApprovalStoreData(body);
    return response;
  }

  @Get('areaAzureTenant')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AreaAzureTenantModel> })
  public async getAreaAzureTenantData(
    @Query() query?: any,
  ): Promise<AreaAzureTenantModel[]> {
    // -- not used
    let {
      azure_tenant_id,
      client_id,
      details_id,
      ignore_decrypt,
      notset,
      returnalliflinked,
      site_id,
    } = query;
    let response = await this.service.getAreaAzureTenantData(
      azure_tenant_id,
      client_id,
      details_id,
      ignore_decrypt,
      notset,
      returnalliflinked,
      site_id,
    );
    return response;
  }

  @Get('attachment')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AttachmentModel })
  public async getAttachment(@Query() query?: any): Promise<AttachmentModel> {
    // -- not used
    let {
      action_id,
      domotzagents,
      filetype,
      idonly,
      isxlsimport,
      one_attachment_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      portal,
      ticket_id,
      token,
      type,
      unique_id,
    } = query;
    let response = await this.service.getAttachment(
      action_id,
      domotzagents,
      filetype,
      idonly,
      isxlsimport,
      one_attachment_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      portal,
      ticket_id,
      token,
      type,
      unique_id,
    );
    return response;
  }

  @Post('attachment')
  @ApiBody({ type: AttachmentModel })
  @ApiResponse({ type: AttachmentModel })
  public async postAttachment(
    @Body() body: AttachmentModel,
  ): Promise<AttachmentModel> {
    // -- not used
    let response = await this.service.postAttachment(body);
    return response;
  }

  @Get('attachment/:id')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AttachmentModel })
  public async getAttachmentById(
    @Query() query?: any,
    @Param('id') id?: string,
  ): Promise<any> {
    // -- not used
    let { childticketid, includedetails, token } = query;
    let response = await this.service.getAttachmentById(
      id,
      childticketid,
      includedetails,
      token,
    );
    return response;
  }

  @Get('attachment/image/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getImageById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id } = query;
    let response = await this.service.getImageById(id);
    return response;
  }

  @Get('attachment/image')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getImage(@Query() query?: any): Promise<any> {
    // -- not used
    let { token, nonce } = query;
    let response = await this.service.getImage(token, nonce);
    return response;
  }

  @Post('attachment/image')
  public async postImage(@Body() body: any): Promise<any> {
    // -- not used
    let response = await this.service.postImage(body);
    return response;
  }

  @Get('attachment/document/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AttachmentModel })
  public async getDocumentById(@Query() query?: any): Promise<AttachmentModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getDocumentById(id);
    return response;
  }

  @Post('attachment/document')
  @ApiBody({ type: AttachmentModel })
  @ApiResponse({ type: AttachmentModel })
  public async postAllDocuments(
    @Body() body: AttachmentModel,
  ): Promise<AttachmentModel> {
    // -- not used
    let response = await this.service.postAllDocuments(body);
    return response;
  }

  @Get('audit')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AuditModel> })
  public async getAudit(@Query() query?: any): Promise<AuditModel[]> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getAudit();
    return response;
  }

  @Post('audit')
  @ApiBody({ type: AuditModel })
  @ApiResponse({ type: AuditModel })
  public async postAudit(@Body() body: AuditModel): Promise<AuditModel> {
    // -- not used
    let response = await this.service.postAudit(body);
    return response;
  }

  @Get('audit/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AuditModel })
  public async getAuditById(@Query() query?: any): Promise<AuditModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getAuditById(id, includedetails);
    return response;
  }

  @Get('ticketRules')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTicketRules(@Query() query?: any): Promise<any[]> {
    // -- not used
    let {
      access_control_level,
      excludeworkflow,
      includecriteriainfo,
      isconfig,
      rule_use,
    } = query;
    let response = await this.service.getTicketRules(
      access_control_level,
      excludeworkflow,
      includecriteriainfo,
      isconfig,
      rule_use,
    );
    return response;
  }

  @Post('ticketRules')
  @ApiBody({ type: AutoassignModel })
  @ApiResponse({ type: AutoassignModel })
  public async postTicketRules(
    @Body() body: AutoassignModel,
  ): Promise<AutoassignModel> {
    // -- not used
    let response = await this.service.postTicketRules(body);
    return response;
  }

  @Get('ticketRules/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTicketRuleById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getTicketRuleById(id, includedetails);
    return response;
  }

  @Get('azureadconnection')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AzureADConnectionModel> })
  public async getAzureAdConnection(
    @Query() query?: any,
  ): Promise<AzureADConnectionModel[]> {
    // -- not used
    let { authorized, isintune, type, types } = query;
    let response = await this.service.getAzureAdConnection(
      authorized,
      isintune,
      type,
      types,
    );
    return response;
  }

  @Post('azureadconnection')
  @ApiBody({ type: AzureADConnectionModel })
  @ApiResponse({ type: AzureADConnectionModel })
  public async postAzureAdConnection(
    @Body() body: AzureADConnectionModel,
  ): Promise<AzureADConnectionModel> {
    // -- not used
    let response = await this.service.postAzureAdConnection(body);
    return response;
  }

  @Get('azureadconnection/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AzureADConnectionModel })
  public async getAzureAdConnectionById(
    @Query() query?: any,
  ): Promise<AzureADConnectionModel> {
    // -- not used
    let { id, includedetails, includetenants } = query;
    let response = await this.service.getAzureAdConnectionById(
      id,
      includedetails,
      includetenants,
    );
    return response;
  }

  @Get('azureadmapping')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AzureADMappingModel> })
  public async getAzureAdMapping(
    @Query() query?: any,
  ): Promise<AzureADMappingModel[]> {
    // -- not used
    let { connection_id } = query;
    let response = await this.service.getAzureAdMapping(connection_id);
    return response;
  }

  @Get('currency')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<CurrencyModel> })
  public async getCurrency(@Query() query?: any): Promise<CurrencyModel[]> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getCurrency();
    return response;
  }

  @Post('currency')
  @ApiBody({ type: CurrencyModel })
  @ApiResponse({ type: CurrencyModel })
  public async postCurrency(
    @Body() body: CurrencyModel,
  ): Promise<CurrencyModel> {
    // -- not used
    let response = await this.service.postCurrency(body);
    return response;
  }

  @Get('currency/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: CurrencyModel })
  public async getCurrencyById(@Query() query?: any): Promise<CurrencyModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getCurrencyById(id, includedetails);
    return response;
  }

  @Get('customTable')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<CustomTableModel> })
  public async getCustomTable(
    @Query() query?: any,
  ): Promise<CustomTableModel[]> {
    // -- not used
    let {
      access_control_level,
      customonly,
      isconfig,
      iswebhookmapping,
      systemonly,
      usage,
    } = query;
    let response = await this.service.getCustomTable(
      access_control_level,
      customonly,
      isconfig,
      iswebhookmapping,
      systemonly,
      usage,
    );
    return response;
  }

  @Post('customTable')
  @ApiBody({ type: CustomTableModel })
  @ApiResponse({ type: CustomTableModel })
  public async postCustomTable(
    @Body() body: CustomTableModel,
  ): Promise<CustomTableModel> {
    // -- not used
    let response = await this.service.postCustomTable(body);
    return response;
  }

  @Get('customTable/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: CustomTableModel })
  public async getCustomTableById(
    @Query() query?: any,
  ): Promise<CustomTableModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getCustomTableById(id, includedetails);
    return response;
  }

  @Get('asset')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getAsset(@Query() query?: any): Promise<any[]> {
    // -- not used
    let {
      activeinactive,
      advanced_search,
      assetgroup_id,
      assetgroups,
      assets,
      assetstatuses,
      assettype,
      assettype_id,
      assettypes,
      bookmarked,
      client_id,
      columns_id,
      consignable,
      consignment_id,
      contract_id,
      contract_id_adding_to,
      count,
      domotzagents,
      excludethese,
      globalSearchID,
      idonly,
      includeactive,
      includeallowedstatus,
      includeassetfields,
      includechildren,
      includecolumns,
      includeinactive,
      includeservices,
      includeuser,
      integration_tenantids,
      integration_type,
      inventory_number,
      islogonbehalfview,
      item_id,
      itemstock_id,
      kb_id,
      lastupdatefromdate,
      lastupdatetodate,
      licence_id,
      linked_to_ticket,
      linkedto_id,
      mine,
      mysite,
      noicon,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      previously_selected,
      previously_selected_client_id,
      previously_selected_site_id,
      previously_selected_user_id,
      salesorder_id,
      salesorder_line,
      search,
      search_inventory_number_only,
      service_id,
      service_ids,
      site_id,
      stockbin_id,
      stockbin_ids,
      supplier_contract_id,
      supplier_id,
      suppliercontracts,
      ticket_id,
      tickettype_id,
      user_id,
      username,
    } = query;
    let response = await this.service.getAsset(
      activeinactive,
      advanced_search,
      assetgroup_id,
      assetgroups,
      assets,
      assetstatuses,
      assettype,
      assettype_id,
      assettypes,
      bookmarked,
      client_id,
      columns_id,
      consignable,
      consignment_id,
      contract_id,
      contract_id_adding_to,
      count,
      domotzagents,
      excludethese,
      globalSearchID,
      idonly,
      includeactive,
      includeallowedstatus,
      includeassetfields,
      includechildren,
      includecolumns,
      includeinactive,
      includeservices,
      includeuser,
      integration_tenantids,
      integration_type,
      inventory_number,
      islogonbehalfview,
      item_id,
      itemstock_id,
      kb_id,
      lastupdatefromdate,
      lastupdatetodate,
      licence_id,
      linked_to_ticket,
      linkedto_id,
      mine,
      mysite,
      noicon,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      previously_selected,
      previously_selected_client_id,
      previously_selected_site_id,
      previously_selected_user_id,
      salesorder_id,
      salesorder_line,
      search,
      search_inventory_number_only,
      service_id,
      service_ids,
      site_id,
      stockbin_id,
      stockbin_ids,
      supplier_contract_id,
      supplier_id,
      suppliercontracts,
      ticket_id,
      tickettype_id,
      user_id,
      username,
    );
    return response;
  }

  @Post('asset')
  @ApiBody({ type: DeviceModel })
  @ApiResponse({ type: DeviceModel })
  public async postAsset(@Body() body: DeviceModel): Promise<DeviceModel> {
    // -- not used
    let response = await this.service.postAsset(body);
    return response;
  }

  @Get('asset/:ucid')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getAssetById(
    @Param() params: any,
    @Query() query?: any,
  ): Promise<any> {
    let { ucid: id } = params;
    // -- not used
    let {
      assettype_id,
      includeactivity,
      includeallowedstatus,
      includedetails,
      includediagramdetails,
      includehierarchy,
    } = query;
    let response = await this.service.getAssetById(
      id ?? '',

      assettype_id,
      includeactivity,
      includeallowedstatus,
      includedetails,
      includediagramdetails,
      includehierarchy,
    );
    return response;
  }

  // ...............
  @Get('asset/:client_id')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getAssetClById(
    @Param() params: any,
    @Query() query?: any,
  ): Promise<any> {
    let { uid: client_id } = params;
    // -- not used
    let {
      assettype_id,
      includeactivity,
      includeallowedstatus,
      includedetails,
      includediagramdetails,
      includehierarchy,
    } = query;
    let response = await this.service.getAssetClById(
      // id??"",
      client_id ?? '',
      assettype_id,
      includeactivity,
      includeallowedstatus,
      includedetails,
      includediagramdetails,
      includehierarchy,
    );
    return response;
  }

  @Get('externalLink')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<ExternalLink_ListModel> })
  public async getExternalLink(
    @Query() query?: any,
  ): Promise<ExternalLink_ListModel[]> {
    // -- not used
    let {
      count,
      details_id,
      halo_id,
      module_id,
      module_list,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      table_id,
      third_party_desc,
      third_party_id,
      third_party_secondary_id,
      third_party_type,
    } = query;
    let response = await this.service.getExternalLink(
      count,
      details_id,
      halo_id,
      module_id,
      module_list,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      table_id,
      third_party_desc,
      third_party_id,
      third_party_secondary_id,
      third_party_type,
    );
    return response;
  }

  @Post('externalLink')
  @ApiBody({ type: ExternalLink_ListModel })
  @ApiResponse({ type: ExternalLink_ListModel })
  public async postExternalLink(
    @Body() body: ExternalLink_ListModel,
  ): Promise<ExternalLink_ListModel> {
    // -- not used
    let response = await this.service.postExternalLink(body);
    return response;
  }

  @Get('externalLink/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ExternalLink_ListModel })
  public async getExternalLinkById(
    @Query() query?: any,
  ): Promise<ExternalLink_ListModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getExternalLinkById(id, includedetails);
    return response;
  }

  @Get('fAQLists')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<FAQListHeadModel> })
  public async getFAQLists(@Query() query?: any): Promise<FAQListHeadModel[]> {
    // -- not used
    let {
      allgroups,
      endoftreeonly,
      level,
      organisation_id,
      parent_id,
      showcounts,
      type,
    } = query;
    let response = await this.service.getFAQLists(
      allgroups,
      endoftreeonly,
      level,
      organisation_id,
      parent_id,
      showcounts,
      type,
    );
    return response;
  }

  @Post('fAQLists')
  @ApiBody({ type: FAQListHeadModel })
  @ApiResponse({ type: FAQListHeadModel })
  public async postFAQLists(
    @Body() body: FAQListHeadModel,
  ): Promise<FAQListHeadModel> {
    // -- not used
    let response = await this.service.postFAQLists(body);
    return response;
  }

  @Get('fAQLists/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<FAQListHeadModel> })
  public async getFAQListsById(
    @Query() query?: any,
  ): Promise<FAQListHeadModel[]> {
    // -- not used
    let { id, includedetails, organisation_id } = query;
    let response = await this.service.getFAQListsById(
      id,
      includedetails,
      organisation_id,
    );
    return response;
  }

  @Get('ticketApproval')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTicketApproval(@Query() query?: any): Promise<any[]> {
    // -- not used
    let {
      action_number,
      include_agent_details,
      include_attachments,
      includeapprovaldetails,
      mine,
      ticket_id,
    } = query;
    let response = await this.service.getTicketApproval({
      action_number,
      include_agent_details,
      include_attachments,
      includeapprovaldetails,
      mine,
      ticket_id,
    });
    return response;
  }

  @Post('ticketApproval')
  @ApiBody({ type: FaultApprovalModel })
  @ApiResponse({ type: FaultApprovalModel })
  public async postTicketApproval(
    @Body() body: FaultApprovalModel,
  ): Promise<FaultApprovalModel> {
    // -- not used
    let response = await this.service.postTicketApproval(body);
    return response;
  }

  @Get('ticketApproval/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getTicketApprovalById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getTicketApprovalById(id, includedetails);
    return response;
  }

  @Get('fieldGroup')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<FieldGroupModel> })
  public async getFieldGroup(@Query() query?: any): Promise<FieldGroupModel[]> {
    // -- not used
    let { access_control_level, includefields, isconfig } = query;
    let response = await this.service.getFieldGroup(
      access_control_level,
      includefields,
      isconfig,
    );
    return response;
  }

  @Post('fieldGroup')
  @ApiBody({ type: FieldGroupModel })
  @ApiResponse({ type: FieldGroupModel })
  public async postFieldGroup(
    @Body() body: FieldGroupModel,
  ): Promise<FieldGroupModel> {
    // -- not used
    let response = await this.service.postFieldGroup(body);
    return response;
  }

  @Get('fieldGroup/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: FieldGroupModel })
  public async getFieldGroupById(
    @Query() query?: any,
  ): Promise<FieldGroupModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getFieldGroupById(id, includedetails);
    return response;
  }

  @Get('fieldInfo')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<FieldInfoModel> })
  public async getFieldInfo(@Query() query?: any): Promise<FieldInfoModel[]> {
    // -- not used
    let {
      access_control_level,
      domain,
      excluderanges,
      excludetables,
      excludetableself,
      extratype,
      fieldtype,
      fieldtypemultiple,
      includecategories,
      includedatefields,
      includejirafields,
      includeremotefields,
      includevalues,
      inputtype,
      isapprovalstep,
      isconfig,
      iscustomfieldsetup,
      systemid,
      typeid,
    } = query;
    let response = await this.service.getFieldInfo(
      access_control_level,
      domain,
      excluderanges,
      excludetables,
      excludetableself,
      extratype,
      fieldtype,
      fieldtypemultiple,
      includecategories,
      includedatefields,
      includejirafields,
      includeremotefields,
      includevalues,
      inputtype,
      isapprovalstep,
      isconfig,
      iscustomfieldsetup,
      systemid,
      typeid,
    );
    return response;
  }

  @Post('fieldInfo')
  @ApiBody({ type: FieldInfoModel })
  @ApiResponse({ type: FieldInfoModel })
  public async postFieldInfo(
    @Body() body: FieldInfoModel,
  ): Promise<FieldInfoModel> {
    // -- not used
    let response = await this.service.postFieldInfo(body);
    return response;
  }

  @Get('fieldInfo/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: FieldInfoModel })
  public async getFieldInfoById(@Query() query?: any): Promise<FieldInfoModel> {
    // -- not used
    let {
      id,
      entityid,
      getlookupvalues,
      includedetails,
      livecustomfields,
      userid,
    } = query;
    let response = await this.service.getFieldInfoById(
      id,
      entityid,
      getlookupvalues,
      includedetails,
      livecustomfields,
      userid,
    );
    return response;
  }

  @Get('integrationData/get/lansweeper')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: LansweeperSoftwareModel })
  public async getLansweeper(
    @Query() query?: any,
  ): Promise<LansweeperSoftwareModel> {
    // -- not used
    let { datatype, exportid, exportUrl, halositeid, siteid } = query;
    let response = await this.service.getLansweeper(
      datatype,
      exportid,
      exportUrl,
      halositeid,
      siteid,
    );
    return response;
  }

  @Get('integrationData/get/qualys')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: QualysHostAssetSoftwareHostAssetSoftwareModel })
  public async getQualys(
    @Query() query?: any,
  ): Promise<QualysHostAssetSoftwareHostAssetSoftwareModel> {
    // -- not used
    let { mappingid, offset, paginate, resource } = query;
    let response = await this.service.getQualys(
      mappingid,
      offset,
      paginate,
      resource,
    );
    return response;
  }

  @Get('integrationFieldMapping')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: IntegrationFieldMappingModel })
  public async getIntegrationFieldMapping(
    @Query() query?: any,
  ): Promise<IntegrationFieldMappingModel> {
    // -- not used
    let { msid, product_id, subtypeid, syncfields, typeid, xmvalue } = query;
    let response = await this.service.getIntegrationFieldMapping(
      msid,
      product_id,
      subtypeid,
      syncfields,
      typeid,
      xmvalue,
    );
    return response;
  }

  @Get('invoiceChange')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoiceChangeModel })
  public async getInvoiceChange(
    @Query() query?: any,
  ): Promise<InvoiceChangeModel> {
    // -- not used
    let {
      count,
      idonly,
      invoice_id,
      line_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      type_id,
    } = query;
    let response = await this.service.getInvoiceChange(
      count,
      idonly,
      invoice_id,
      line_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      type_id,
    );
    return response;
  }

  @Post('invoiceChange')
  @ApiBody({ type: InvoiceChangeModel })
  @ApiResponse({ type: InvoiceChangeModel })
  public async postInvoiceChange(
    @Body() body: InvoiceChangeModel,
  ): Promise<InvoiceChangeModel> {
    // -- not used
    let response = await this.service.postInvoiceChange(body);
    return response;
  }

  @Get('invoiceDetailProRata')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoiceDetailProRataModel })
  public async getInvoiceDetailProRata(
    @Query() query?: any,
  ): Promise<InvoiceDetailProRataModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getInvoiceDetailProRata();
    return response;
  }

  @Get('invoice')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoiceHeaderModel })
  public async getInvoice(@Query() query?: any): Promise<InvoiceHeaderModel> {
    // -- not used
    let {
      advanced_search,
      asset_id,
      awaiting_approval,
      billing_date,
      billingcategory_ids,
      client_id,
      client_ids,
      contract_id,
      count,
      idonly,
      includecredits,
      includeinvoices,
      includelines,
      includepoinvoices,
      invoicedateend,
      invoicedatestart,
      my_approvals,
      notpostedonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      paymentstatuses,
      postedonly,
      purchaseorder_id,
      quote_status,
      ready_for_invoicing,
      recurringinvoice_id,
      reviewrequired,
      rinvoice_type,
      salesorder_id,
      search,
      sent_status,
      site_id,
      stripeautopaymentrequired,
      ticket_id,
      toplevel_id,
      user_id,
      third_party_id,
      xero_id,
      quickbooks_id,
    } = query;
    let response = await this.service.getInvoice(
      advanced_search,
      asset_id,
      awaiting_approval,
      billing_date,
      billingcategory_ids,
      client_id,
      client_ids,
      contract_id,
      count,
      idonly,
      includecredits,
      includeinvoices,
      includelines,
      includepoinvoices,
      invoicedateend,
      invoicedatestart,
      my_approvals,
      notpostedonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      paymentstatuses,
      postedonly,
      purchaseorder_id,
      quote_status,
      ready_for_invoicing,
      recurringinvoice_id,
      reviewrequired,
      rinvoice_type,
      salesorder_id,
      search,
      sent_status,
      site_id,
      stripeautopaymentrequired,
      ticket_id,
      toplevel_id,
      user_id,
      third_party_id,
      xero_id,
      quickbooks_id,
    );
    return response;
  }

  @Post('invoice')
  @ApiBody({ type: InvoiceHeaderModel })
  @ApiResponse({ type: InvoiceHeaderModel })
  public async postInvoice(
    @Body() body: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    let response = await this.service.postInvoice(body);
    return response;
  }

  @Get('invoice/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoiceHeaderModel })
  public async getInvoiceById(
    @Query() query?: any,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getInvoiceById(id, includedetails);
    return response;
  }

  @Post('invoice/updatelines')
  @ApiBody({ type: InvoiceDetailModel })
  @ApiResponse({ type: InvoiceDetailModel })
  public async postInvoiceUpdatelines(
    @Body() body: InvoiceDetailModel,
  ): Promise<InvoiceDetailModel> {
    // -- not used
    let response = await this.service.postInvoiceUpdatelines(body);
    return response;
  }

  @Post('invoice/{id}/void')
  @ApiBody({ type: InvoiceHeaderModel })
  @ApiResponse({ type: InvoiceHeaderModel })
  public async postInvoiceVoidById(
    @Body() body: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    let response = await this.service.postInvoiceVoidById(body);
    return response;
  }

  @Post('invoice/pDF/{id}')
  @ApiBody({ type: InvoiceHeaderModel })
  @ApiResponse({ type: InvoiceHeaderModel })
  public async postInvoicePdfById(
    @Body() body: InvoiceHeaderModel,
  ): Promise<InvoiceHeaderModel> {
    // -- not used
    let response = await this.service.postInvoicePdfById(body);
    return response;
  }

  @Get('invoicePayment')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoicePayment_ListModel })
  public async getInvoicePayment(
    @Query() query?: any,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    let {
      client_id,
      count,
      intent_id,
      invoice_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
    } = query;
    let response = await this.service.getInvoicePayment(
      client_id,
      count,
      intent_id,
      invoice_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
    );
    return response;
  }

  @Post('invoicePayment')
  @ApiBody({ type: InvoicePayment_ListModel })
  @ApiResponse({ type: InvoicePayment_ListModel })
  public async postInvoicePayment(
    @Body() body: InvoicePayment_ListModel,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    let response = await this.service.postInvoicePayment(body);
    return response;
  }

  @Get('invoicePayment/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: InvoicePayment_ListModel })
  public async getInvoicePaymentById(
    @Query() query?: any,
  ): Promise<InvoicePayment_ListModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getInvoicePaymentById(id, includedetails);
    return response;
  }

  @Get('item')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemModel })
  public async getItem(@Query() query?: any): Promise<ItemModel> {
    // -- not used
    let {
      activeinactive,
      advanced_search,
      assetgroup_id,
      assetgroups,
      assettypes,
      autotask_service_items,
      count,
      dbc_company_id,
      exactdivision,
      excluderecurring,
      includeactive,
      includeinactive,
      include_custom_fields,
      itemservice_id,
      itemservicerequestdetails_id,
      itemsupplierclientid,
      kashflowtenantid,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      qbitemsonly,
      qbocompanyid,
      recurringonly,
      sagebusinesscloudtenantid,
      search,
      search1,
      show_not_in_stock,
      stocklocation_id,
      supplier_id,
      xerotenantid,
    } = query;
    let response = await this.service.getItem(
      activeinactive,
      advanced_search,
      assetgroup_id,
      assetgroups,
      assettypes,
      autotask_service_items,
      count,
      dbc_company_id,
      exactdivision,
      excluderecurring,
      includeactive,
      includeinactive,
      include_custom_fields,
      itemservice_id,
      itemservicerequestdetails_id,
      itemsupplierclientid,
      kashflowtenantid,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      qbitemsonly,
      qbocompanyid,
      recurringonly,
      sagebusinesscloudtenantid,
      search,
      search1,
      show_not_in_stock,
      stocklocation_id,
      supplier_id,
      xerotenantid,
    );
    return response;
  }

  @Post('item')
  @ApiBody({ type: ItemModel })
  @ApiResponse({ type: ItemModel })
  public async postItem(@Body() body: ItemModel): Promise<ItemModel> {
    // -- not used
    let response = await this.service.postItem(body);
    return response;
  }

  @Get('item/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemModel })
  public async getItemById(@Query() query?: any): Promise<ItemModel> {
    // -- not used
    let {
      id,
      dbc_company_id,
      includedetails,
      kashflowtenantid,
      qbocompanyid,
      sagebusinesscloudtenantid,
      xerotenantid,
    } = query;
    let response = await this.service.getItemById(
      id,
      dbc_company_id,
      includedetails,
      kashflowtenantid,
      qbocompanyid,
      sagebusinesscloudtenantid,
      xerotenantid,
    );
    return response;
  }

  @Post('item/newAccountsId')
  @ApiBody({ type: ItemModel })
  @ApiResponse({ type: ItemModel })
  public async postItemNewAccountsId(
    @Body() body: ItemModel,
  ): Promise<ItemModel> {
    // -- not used
    let response = await this.service.postItemNewAccountsId(body);
    return response;
  }

  @Get('itemStock')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemStockModel })
  public async getItemStock(@Query() query?: any): Promise<ItemStockModel> {
    // -- not used
    let {
      count,
      idonly,
      item_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      stockbin_id,
      stocklocation_id,
    } = query;
    let response = await this.service.getItemStock(
      count,
      idonly,
      item_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      stockbin_id,
      stocklocation_id,
    );
    return response;
  }

  @Post('itemStock')
  @ApiBody({ type: ItemStockModel })
  @ApiResponse({ type: ItemStockModel })
  public async postItemStock(
    @Body() body: ItemStockModel,
  ): Promise<ItemStockModel> {
    // -- not used
    let response = await this.service.postItemStock(body);
    return response;
  }

  @Get('itemStock/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemStockModel })
  public async getItemStockById(@Query() query?: any): Promise<ItemStockModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getItemStockById(id, includedetails);
    return response;
  }

  @Get('itemStockHistory')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getItemStockHistory(@Query() query?: any): Promise<any> {
    // -- not used
    let {
      count,
      idonly,
      item_id,
      itemstock_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      stockbin_id,
      stocklocation_id,
    } = query;
    let response = await this.service.getItemStockHistory(
      count,
      idonly,
      item_id,
      itemstock_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      search,
      stockbin_id,
      stocklocation_id,
    );
    return response;
  }

  @Get('itemStockHistory/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getItemStockHistoryById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id } = query;
    let response = await this.service.getItemStockHistoryById(id);
    return response;
  }

  @Get('itemsupplier')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemSupplierModel })
  public async getItemsupplier(
    @Query() query?: any,
  ): Promise<ItemSupplierModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getItemsupplier();
    return response;
  }

  @Post('itemsupplier')
  @ApiBody({ type: ItemSupplierModel })
  @ApiResponse({ type: ItemSupplierModel })
  public async postItemsupplier(
    @Body() body: ItemSupplierModel,
  ): Promise<ItemSupplierModel> {
    // -- not used
    let response = await this.service.postItemsupplier(body);
    return response;
  }

  @Get('itemsupplier/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemSupplierModel })
  public async getItemsupplierById(
    @Query() query?: any,
  ): Promise<ItemSupplierModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getItemsupplierById(id, includedetails);
    return response;
  }

  @Get('softwareLicence/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Licence_ListModel })
  public async getSoftwareLicenceById(
    @Query() query?: any,
  ): Promise<Licence_ListModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getSoftwareLicenceById(
      id,
      includedetails,
    );
    return response;
  }

  @Get('softwareLicenceRole')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: LicenceRoleModel })
  public async getSoftwareLicenceRole(
    @Query() query?: any,
  ): Promise<LicenceRoleModel> {
    // -- not used
    let { softwarelicence_id } = query;
    let response =
      await this.service.getSoftwareLicenceRole(softwarelicence_id);
    return response;
  }

  @Get('mailCampaignLog')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: FormattedEmailModel })
  public async getMailCampaignLog(
    @Query() query?: any,
  ): Promise<FormattedEmailModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getMailCampaignLog();
    return response;
  }

  @Get('mailCampaignLog/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: FormattedEmailModel })
  public async getMailCampaignLogById(
    @Query() query?: any,
  ): Promise<FormattedEmailModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getMailCampaignLogById(id);
    return response;
  }

  @Get('marketingUnsubscribe')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: MarketingUnsubscribeModel })
  public async getMarketingUnsubscribe(
    @Query() query?: any,
  ): Promise<MarketingUnsubscribeModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getMarketingUnsubscribe();
    return response;
  }

  @Post('marketingUnsubscribe')
  @ApiBody({ type: MarketingUnsubscribeModel })
  @ApiResponse({ type: MarketingUnsubscribeModel })
  public async postMarketingUnsubscribe(
    @Body() body: MarketingUnsubscribeModel,
  ): Promise<MarketingUnsubscribeModel> {
    // -- not used
    let response = await this.service.postMarketingUnsubscribe(body);
    return response;
  }

  @Get('marketingUnsubscribe/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: MarketingUnsubscribeModel })
  public async getMarketingUnsubscribeById(
    @Query() query?: any,
  ): Promise<MarketingUnsubscribeModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getMarketingUnsubscribeById(id);
    return response;
  }

  @Get('features')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getFeatures(@Query() query?: any): Promise<any> {
    // -- not used
    let { isconfig, showdisabled, showenabled } = query;
    let response = await this.service.getFeatures(
      isconfig,
      showdisabled,
      showenabled,
    );
    return response;
  }

  @Post('features')
  @ApiBody({ type: ModuleSetupModel })
  @ApiResponse({ type: ModuleSetupModel })
  public async postFeatures(
    @Body() body: ModuleSetupModel,
  ): Promise<ModuleSetupModel> {
    // -- not used
    let response = await this.service.postFeatures(body);
    return response;
  }

  @Get('features/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getFeaturesById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getFeaturesById(id, includedetails);
    return response;
  }

  @Get('haloDeviceInfo/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DeviceModel })
  public async getHaloDeviceInfoById(
    @Query() query?: any,
  ): Promise<DeviceModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getHaloDeviceInfoById(id);
    return response;
  }

  @Post('haloDeviceInfo')
  @ApiBody({ type: NHD_DeviceInfoModel })
  @ApiResponse({ type: NHD_DeviceInfoModel })
  public async postHaloDeviceInfo(
    @Body() body: NHD_DeviceInfoModel,
  ): Promise<NHD_DeviceInfoModel> {
    // -- not used
    let response = await this.service.postHaloDeviceInfo(body);
    return response;
  }

  @Get('outgoing')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getOutgoing(@Query() query?: any): Promise<any[]> {
    // -- not used
    let {
      count,
      idonly,
      mailbox_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      status_id,
    } = query;
    let response = await this.service.getOutgoing(
      count,
      idonly,
      mailbox_id,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      status_id,
    );
    return response;
  }

  @Post('outgoing')
  public async postOutgoing(@Body() body: any): Promise<any> {
    // -- not used
    let response = await this.service.postOutgoing(body);
    return response;
  }

  @Get('outgoing/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getOutgoingById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id, includeattachments, includedetails } = query;
    let response = await this.service.getOutgoingById(
      id,
      includeattachments,
      includedetails,
    );
    return response;
  }

  @Get('outgoingAttempt')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getOutgoingAttempt(@Query() query?: any): Promise<any[]> {
    // -- not used
    let {
      count,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      outgoing_id,
      page_no,
      page_size,
      pageinate,
    } = query;
    let response = await this.service.getOutgoingAttempt(
      count,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      outgoing_id,
      page_no,
      page_size,
      pageinate,
    );
    return response;
  }

  @Get('outgoingAttempt/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getOutgoingAttemptById(@Query() query?: any): Promise<any> {
    // -- not used
    let { id, includeattachments, includedetails } = query;
    let response = await this.service.getOutgoingAttemptById(
      id,
      includeattachments,
      includedetails,
    );
    return response;
  }

  @Get('databaseLookup')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<DbTypeModel> })
  public async getDatabaseLookup(@Query() query?: any): Promise<DbTypeModel[]> {
    // -- not used
    let { type } = query;
    let response = await this.service.getDatabaseLookup(type);
    return response;
  }

  @Post('databaseLookup')
  @ApiBody({ type: PartsLookupModel })
  @ApiResponse({ type: PartsLookupModel })
  public async postDatabaseLookup(
    @Body() body: PartsLookupModel,
  ): Promise<PartsLookupModel> {
    // -- not used
    let response = await this.service.postDatabaseLookup(body);
    return response;
  }

  @Get('databaseLookup/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DbTypeModel })
  public async getDatabaseLookupById(
    @Query() query?: any,
  ): Promise<DbTypeModel> {
    // -- not used
    let { id, includedetails, lookup_value } = query;
    let response = await this.service.getDatabaseLookupById(
      id,
      includedetails,
      lookup_value,
    );
    return response;
  }

  @Post('databaseLookup/run')
  @ApiBody({ type: PartsLookupModel })
  @ApiResponse({ type: PartsLookupModel })
  public async postDatabaseLookupRun(
    @Body() body: PartsLookupModel,
  ): Promise<PartsLookupModel> {
    // -- not used
    let response = await this.service.postDatabaseLookupRun(body);
    return response;
  }

  @Get('quickBooksDetails')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: QuickBooksDetailsModel })
  public async getQuickBooksDetails(
    @Query() query?: any,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    let { companyid, connectedonly } = query;
    let response = await this.service.getQuickBooksDetails(
      companyid,
      connectedonly,
    );
    return response;
  }

  @Post('quickBooksDetails')
  @ApiBody({ type: QuickBooksDetailsModel })
  @ApiResponse({ type: QuickBooksDetailsModel })
  public async postQuickBooksDetails(
    @Body() body: QuickBooksDetailsModel,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    let response = await this.service.postQuickBooksDetails(body);
    return response;
  }

  @Get('quickBooksDetails/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: QuickBooksDetailsModel })
  public async getQuickBooksDetailsById(
    @Query() query?: any,
  ): Promise<QuickBooksDetailsModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getQuickBooksDetailsById(
      id,
      includedetails,
    );
    return response;
  }

  @Get('product')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemModel })
  public async getProduct(@Query() query?: any): Promise<ItemModel> {
    // -- not used
    let { devops_instance, third_party_only } = query;
    let response = await this.service.getProduct(
      devops_instance,
      third_party_only,
    );
    return response;
  }

  @Post('product')
  @ApiBody({ type: ReleaseProductModel })
  @ApiResponse({ type: ReleaseProductModel })
  public async postProduct(
    @Body() body: ReleaseProductModel,
  ): Promise<ReleaseProductModel> {
    // -- not used
    let response = await this.service.postProduct(body);
    return response;
  }

  @Get('product/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ItemModel })
  public async getProductById(@Query() query?: any): Promise<ItemModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getProductById(id, includedetails);
    return response;
  }

  @Get('ticketTypeField')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: RequestTypeFieldModel })
  public async getTicketTypeField(
    @Query() query?: any,
  ): Promise<RequestTypeFieldModel> {
    // -- not used
    let { buildcache, debug, isrtconfig } = query;
    let response = await this.service.getTicketTypeField(
      buildcache,
      debug,
      isrtconfig,
    );
    return response;
  }

  @Get('roadmap')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: WorkflowTargetModel })
  public async getRoadmap(@Query() query?: any): Promise<WorkflowTargetModel> {
    // -- not used
    let {
      halocrm,
      haloitsm,
      halopsa,
      haloservicedesk,
      order,
      orderdesc,
      product_id,
      roadmapcolumnview,
    } = query;
    let response = await this.service.getRoadmap(
      halocrm,
      haloitsm,
      halopsa,
      haloservicedesk,
      order,
      orderdesc,
      product_id,
      roadmapcolumnview,
    );
    return response;
  }

  @Get('scheduleOccurrence')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AuditModel })
  public async getScheduleOccurrence(
    @Query() query?: any,
  ): Promise<AuditModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getScheduleOccurrence();
    return response;
  }

  @Post('scheduleOccurrence')
  @ApiBody({ type: AuditModel })
  @ApiResponse({ type: AuditModel })
  public async postScheduleOccurrence(
    @Body() body: AuditModel,
  ): Promise<AuditModel> {
    // -- not used
    let response = await this.service.postScheduleOccurrence(body);
    return response;
  }

  @Get('scheduleOccurrence/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AuditModel })
  public async getScheduleOccurrenceById(
    @Query() query?: any,
  ): Promise<AuditModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getScheduleOccurrenceById(id);
    return response;
  }

  @Get('serviceRequestDetails')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServiceRequestDetailsModel })
  public async getServiceRequestDetails(
    @Query() query?: any,
  ): Promise<ServiceRequestDetailsModel> {
    // -- not used
    let { exclude_urls, includedetails, service_id } = query;
    let response = await this.service.getServiceRequestDetails(
      exclude_urls,
      includedetails,
      service_id,
    );
    return response;
  }

  @Get('serviceRequestDetails/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServiceRequestDetailsModel })
  public async getServiceRequestDetailsById(
    @Query() query?: any,
  ): Promise<ServiceRequestDetailsModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getServiceRequestDetailsById(
      id,
      includedetails,
    );
    return response;
  }

  @Get('serviceRestriction')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServiceRestrictionModel })
  public async getServiceRestriction(
    @Query() query?: any,
  ): Promise<ServiceRestrictionModel> {
    // -- not used
    let { client_id, service_category_id, service_id } = query;
    let response = await this.service.getServiceRestriction(
      client_id,
      service_category_id,
      service_id,
    );
    return response;
  }

  @Get('service')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServiceUserModel })
  public async getService(@Query() query?: any): Promise<ServiceUserModel> {
    // -- not used
    let {
      access_control_level,
      asset_ids,
      count,
      includechildservices,
      includestatusinfo,
      itil_ticket_type,
      monitoredonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      parent_service_category_id,
      relatedservicesonly,
      search,
      service_category_id,
      service_category_ids,
      service_status_ids,
      subscribedonly,
      template_id,
      ticket_id,
      tickettype_id,
      user_id,
    } = query;
    let response = await this.service.getService(
      access_control_level,
      asset_ids,
      count,
      includechildservices,
      includestatusinfo,
      itil_ticket_type,
      monitoredonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      parent_service_category_id,
      relatedservicesonly,
      search,
      service_category_id,
      service_category_ids,
      service_status_ids,
      subscribedonly,
      template_id,
      ticket_id,
      tickettype_id,
      user_id,
    );
    return response;
  }

  @Get('service/generalRequest')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Object })
  public async getServiceWithGeneralRequestFilter(
    @Query() query?: any,
  ): Promise<any> {
    // -- not used
    let {
      access_control_level,
      asset_ids,
      count,
      includechildservices,
      includestatusinfo,
      itil_ticket_type,
      monitoredonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      parent_service_category_id,
      relatedservicesonly,
      search,
      service_category_id,
      service_category_ids,
      service_status_ids,
      subscribedonly,
      template_id,
      ticket_id,
      tickettype_id,
      user_id,
    } = query;
    let response = await this.service.getService(
      access_control_level,
      asset_ids,
      count,
      includechildservices,
      includestatusinfo,
      itil_ticket_type,
      monitoredonly,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      parent_service_category_id,
      relatedservicesonly,
      search,
      service_category_id,
      service_category_ids,
      service_status_ids,
      subscribedonly,
      template_id,
      ticket_id,
      tickettype_id,
      user_id,
    );
    const filteredService = response?.services?.find((service: any) =>
      service.name.toLowerCase().includes('general request'.toLowerCase()),
    );

    console.log(filteredService?.id);
    if (filteredService?.id != null) {
      const id = filteredService.id;
      let result = await this.service.getServiceById(id, true);
      return result;
    } else {
      return response;
    }
  }

  @Post('service')
  @ApiBody({ type: ServSiteModel })
  @ApiResponse({ type: ServSiteModel })
  public async postService(
    @Body() body: ServSiteModel,
  ): Promise<ServSiteModel> {
    // -- not used
    let response = await this.service.postService(body);
    return response;
  }

  @Get('service/:id')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServiceUserModel })
  public async getServiceById(@Query() query?: any): Promise<ServiceUserModel> {
    // -- not used
    let { id, includedetails, user_id } = query;
    let response = await this.service.getServiceById(
      id,
      includedetails,
      user_id,
    );
    return response;
  }

  @Get('serviceStatus')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServStatusModel })
  public async getServiceStatus(
    @Query() query?: any,
  ): Promise<ServStatusModel> {
    // -- not used
    let {
      count,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      service_id,
    } = query;
    let response = await this.service.getServiceStatus(
      count,
      order,
      order2,
      order3,
      order4,
      order5,
      orderdesc,
      orderdesc2,
      orderdesc3,
      orderdesc4,
      orderdesc5,
      page_no,
      page_size,
      pageinate,
      service_id,
    );
    return response;
  }

  @Post('serviceStatus')
  @ApiBody({ type: ServStatusModel })
  @ApiResponse({ type: ServStatusModel })
  public async postServiceStatus(
    @Body() body: ServStatusModel,
  ): Promise<ServStatusModel> {
    // -- not used
    let response = await this.service.postServiceStatus(body);
    return response;
  }

  @Get('serviceStatus/subscribe/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServStatusSubscribeModel })
  public async getSubscribeServiceStatusById(
    @Query() query?: any,
  ): Promise<ServStatusSubscribeModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getSubscribeServiceStatusById(id);
    return response;
  }

  @Get('serviceStatus/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: ServStatusModel })
  public async getServiceStatusById(
    @Query() query?: any,
  ): Promise<ServStatusModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getServiceStatusById(id, includedetails);
    return response;
  }

  @Post('serviceStatus/subscribe')
  @ApiBody({ type: ServStatusSubscribeModel })
  @ApiResponse({ type: ServStatusSubscribeModel })
  public async postSubscribeServiceStatus(
    @Body() body: ServStatusSubscribeModel,
  ): Promise<ServStatusSubscribeModel> {
    // -- not used
    let response = await this.service.postSubscribeServiceStatus(body);
    return response;
  }

  @Get('tags')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TagModel })
  public async getTags(@Query() query?: any): Promise<TagModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getTags();
    return response;
  }

  @Post('tags')
  @ApiBody({ type: TagModel })
  @ApiResponse({ type: TagModel })
  public async postTags(@Body() body: TagModel): Promise<TagModel> {
    // -- not used
    let response = await this.service.postTags(body);
    return response;
  }

  @Get('tags/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TagModel })
  public async getTagsById(@Query() query?: any): Promise<TagModel> {
    // -- not used
    let { id, includedetails } = query;
    let response = await this.service.getTagsById(id, includedetails);
    return response;
  }

  @Get('workflowTarget')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: WorkflowTargetModel })
  public async getWorkflowTarget(
    @Query() query?: any,
  ): Promise<WorkflowTargetModel> {
    // -- not used
    console.log('No query parameters provided', query);
    let response = await this.service.getWorkflowTarget();
    return response;
  }

  @Post('workflowTarget')
  @ApiBody({ type: WorkflowTargetModel })
  @ApiResponse({ type: WorkflowTargetModel })
  public async postWorkflowTarget(
    @Body() body: WorkflowTargetModel,
  ): Promise<WorkflowTargetModel> {
    // -- not used
    let response = await this.service.postWorkflowTarget(body);
    return response;
  }

  @Get('workflowTarget/${id}')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: WorkflowTargetModel })
  public async getWorkflowTargetById(
    @Query() query?: any,
  ): Promise<WorkflowTargetModel> {
    // -- not used
    let { id } = query;
    let response = await this.service.getWorkflowTargetById(id);
    return response;
  }
}
