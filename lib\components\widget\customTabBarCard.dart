import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/form/bottomSheet.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/dateFormatter.dart';
import 'package:seawork/utils/style/colors.dart';

import 'package:seawork/screens/employee/approval/components/approvalBottomSheet.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';
import 'package:seawork/screens/employee/approval/providers/approvalsProvider.dart';

class ReusableCard extends ConsumerStatefulWidget {
  final TaskListResponse tasks;
  final int selectedTabIndex;
  final String iconClicked;
  final String? status;
  final String selectedStatus;
  final bool isVisible;
  final bool showDownloadButton;
  final bool hideContainerForScreens;
  final bool removeTopBottomPadding;
final bool forAbsence;

  const ReusableCard({
    Key? key,
    required this.tasks,
    required this.hideContainerForScreens,
    required this.selectedTabIndex,
    required this.selectedStatus,
    required this.iconClicked,
    required this.isVisible,
    required this.showDownloadButton,
    this.status,
    this.forAbsence = false,
    this.removeTopBottomPadding = false,
  }) : super(key: key);

  @override
  ConsumerState<ReusableCard> createState() => _ReusableCardState();
}

class _ReusableCardState extends ConsumerState<ReusableCard> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;
  double _lastScrollPosition = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_onScroll);

    // Initialize with current tasks
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(tasksOffsetProvider.notifier).state = 0;
    });
  }

  void _onScroll() {
    final currentOffset = _scrollController.position.pixels;
    final maxExtent = _scrollController.position.maxScrollExtent;
    final viewport = _scrollController.position.viewportDimension;

    // Constants for better readability
    final preloadOffset = 500;
    final isNearBottom = currentOffset >= maxExtent - preloadOffset;
    final isNearTop = currentOffset <= 50;
    final canScroll = maxExtent > viewport;

    // Determine scroll direction
    final isScrollingUp = currentOffset < _lastScrollPosition;
    final isScrollingDown = !isScrollingUp;

    // Debug print
    print(
      'Scroll: offset=$currentOffset, direction=${isScrollingUp ? "UP" : "DOWN"}, '
      'isNearTop=$isNearTop, isNearBottom=$isNearBottom, canScroll=$canScroll',
    );

    _lastScrollPosition = currentOffset;

    // Load more when scrolling down near the bottom
    if (isScrollingDown && isNearBottom && !_isLoadingMore && canScroll) {
      _loadMoreTasks();
    }

    // Load previous when scrolling up near the top
    if (isScrollingUp &&
        isNearTop &&
        !_isLoadingMore &&
        canScroll &&
        ref.read(tasksOffsetProvider) > 0) {
      _loadPreviousTasks();
    }
  }

  Future<void> _loadPreviousTasks() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    final currentOffset = ref.read(tasksOffsetProvider);
    final limit = ref.read(tasksLimitProvider);
    final newOffset = currentOffset > 0 ? currentOffset - limit : 0;

    if (newOffset == currentOffset) {
      setState(() => _isLoadingMore = false);
      return;
    }

    // Update the offset in the provider
    ref.read(tasksOffsetProvider.notifier).state = newOffset;

    try {
      // Call the appropriate notifier based on the selected status
      switch (widget.selectedStatus) {
        case 'ASSIGNED':
          await ref
              .read(pendingTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset, prepend: true );
          break;
        case 'COMPLETED':
          await ref
              .read(approvedTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset, prepend: true);
          break;
        case 'SUSPENDED':
          await ref
              .read(rejectedTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset, prepend: true);
          break;
        case 'WITHDRAWN':
          await ref
              .read(withdrawnTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset, prepend: true);
          break;
      }
    } catch (e) {
      print('Error loading previous tasks: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  Future<void> _loadMoreTasks() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    final currentOffset = ref.read(tasksOffsetProvider);
    final limit = ref.read(tasksLimitProvider);
    final newOffset = currentOffset + limit;

    // Update the offset in the provider
    ref.read(tasksOffsetProvider.notifier).state = newOffset;

    try {
      // Call the appropriate notifier based on the selected status
      switch (widget.selectedStatus) {
        case 'ASSIGNED':
          await ref
              .read(pendingTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset);
          break;
        case 'COMPLETED':
          await ref
              .read(approvedTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset);
          break;
        case 'SUSPENDED':
          await ref
              .read(rejectedTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset);
          break;
        case 'WITHDRAWN':
          await ref
              .read(withdrawnTasksNotifierProvider.notifier)
              .fetchPaginatedData(offset: newOffset);
          break;
      }
    } catch (e) {
      print('Error loading more tasks: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  String formatCreationDate(String isoDateString) {
    try {
      final date = DateTime.parse(isoDateString);

      final monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      final day = date.day;
      final month = monthNames[date.month - 1];
      final year = date.year;

      String getOrdinalSuffix(int day) {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
          case 1:
            return 'st';
          case 2:
            return 'nd';
          case 3:
            return 'rd';
          default:
            return 'th';
        }
      }

      return '$day${getOrdinalSuffix(day)} $month, $year';
    } catch (e) {
      return isoDateString;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the tasks from the appropriate notifier based on status
    final tasksAsync = _getTasksForStatus(widget.selectedStatus,  widget.forAbsence);

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: tasksAsync.when(
        loading: () => const Center(child: CustomLoadingWidget()),
        error:
            (error, stackTrace) => Center(
              child: OpenSansText(
                'Error: $error',
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColors.red,
              ),
            ),
        data:
            (tasks) => Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Top loading indicator
                // if (_isLoadingMore && _lastScrollPosition <= 50)
                //   const Padding(
                //     padding: EdgeInsets.symmetric(vertical: 4.0),
                //     child: LinearProgressIndicator(),
                //   ),

                // Main content
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount:
                        tasks.length +
                        (_isLoadingMore && _lastScrollPosition > 50 ? 1 : 0),
                    itemBuilder: (context, index) {
                      // Bottom loader
                      // if (index == tasks.length &&
                      //     _isLoadingMore &&
                      //     _lastScrollPosition > 50) {
                      //   return const Padding(
                      //     padding: EdgeInsets.symmetric(vertical: 8.0),
                      //     child: Center(child: LinearProgressIndicator()),
                      //   );
                      // }

                      if (index >= tasks.length) {
                        return const SizedBox.shrink();
                      }

                      final task = tasks[index];

                      // Task item UI
                      return GestureDetector(
                        onTap: () {
                          showCustomDraggableModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            context: context,
                            child: ApprovalBottomSheet(
                              taskId: task.number,
                              category: task.taskDefinitionName,
                            ),
                          );
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.shadowBoxColor.withOpacity(
                                  0.25,
                                ),
                                offset: const Offset(0, 0),
                                blurRadius: 9.6,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Padding(
                            padding:
                                widget.removeTopBottomPadding
                                    ? const EdgeInsets.only(left: 12, right: 12)
                                    : const EdgeInsets.only(
                                      left: 12,
                                      right: 12,
                                      top: 14,
                                      bottom: 12,
                                    ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Leave Type
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: DmSansText(
                                        _mapCategoryToName(
                                          task.taskDefinitionName,
                                          task.title,
                                        ),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.blackColor,
                                      ),
                                    ),
                                    // if (!widget.hideContainerForScreens &&
                                    //     widget.iconClicked ==
                                    //         "approval") // Control visibility based on screen
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color:
                                            AppColors
                                                .statuscolour, // Light blue background
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      // child: Text(
                                      child: OpenSansText(
                                        _mapCategoryToType(
                                          task.taskDefinitionName,
                                        ),
                                        fontSize: 10,
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.lightBlack,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        CustomSvgImage(
                                          imageName: "cardcalender",
                                        ),
                                        const SizedBox(width: 8),
                                        OpenSansText(
                                          formatCreationDate(task.createdDate),
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: AppColors.darkGreyColor,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                const Divider(color: AppColors.lightGreyColor3),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (task.taskDefinitionName ==
                                        'DocumentOfRecordApproval')
                                      buildKeyValueRow(
                                        "Requested By",
                                        task.createdBy,
                                      )
                                    else ...[
                                      buildKeyValueRow(
                                        "Submitted by",
                                        task.createdBy,
                                      ),
                                      if (task.category ==
                                          'GlobalAbsenceApproval')
                                        buildKeyValueRow(
                                          "Applied for",
                                          _extractDateRangeFromTitle(
                                            task.title,
                                          ),
                                        ),
                                      buildKeyValueRow(
                                        "Duration",
                                        "${task.approvalDuration ?? 0} days",
                                      ),
                                      if (task.category == 'Compensation')
                                        buildKeyValueRow(
                                          "Dependent Name",
                                          task
                                                  .assignees
                                                  .items
                                                  .firstOrNull
                                                  ?.firstName ??
                                              "N/A",
                                        ),
                                    ],
                                  ],
                                ),
                                const SizedBox(height: 4),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
      ),
    );
  }

  // Helper method to get the appropriate task list based on status
  AsyncValue<List<TaskItem>> _getTasksForStatus(String status,forAbsence) {
    if (forAbsence) {
    // Wrap passed tasks into AsyncValue
    return AsyncValue.data(widget.tasks.items ?? []);
  }

    switch (status) {
      case 'ASSIGNED':
        return ref.watch(pendingTasksNotifierProvider);
      case 'COMPLETED':
        return ref.watch(approvedTasksNotifierProvider);
      case 'SUSPENDED':
        return ref.watch(rejectedTasksNotifierProvider);
      case 'WITHDRAWN':
        return ref.watch(withdrawnTasksNotifierProvider);
      default:
        return const AsyncValue.data([]);
    }
  }

  // Helper Function for Key-Value Display
  Widget buildKeyValueRow(String key, String value, {Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          OpenSansText(
            key,
            fontWeight: FontWeight.w600,
            fontSize: 12,
            color: AppColors.blackColor,
          ),
          SizedBox(height: 24),
          OpenSansText(
            value,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: textColor ?? AppColors.lightGreyColor,
          ),
        ],
      ),
    );
  }

  String _extractDateRangeFromTitle(String title) {
    final regex = RegExp(r'from (\d{4}-\d{2}-\d{2}) to (\d{4}-\d{2}-\d{2})');
    final match = regex.firstMatch(title);

    if (match != null) {
      final startDate = match.group(1);
      final endDate = match.group(2);

      final formattedStart = formatDateWithoutOrdinal(startDate);
      final formattedEnd = formatDateWithoutOrdinal(endDate);

      return '$formattedStart to $formattedEnd';
    }
    return '';
  }

  String _mapCategoryToName(String category, String? taskTitle) {
    switch (category) {
      case 'AbsencesApprovalsTask':
        // Extract leave name from title
        final regex = RegExp(r'Approval of (.*?) Absence Request');
        final match = regex.firstMatch(taskTitle ?? '');
        return match != null ? match.group(1) ?? 'Leave' : 'Leave';
      case 'Compensation':
        return 'Claims';
      case 'DocumentOfRecordApproval':
        // Extract document type from title
        if (taskTitle != null) {
          final regex = RegExp(r'\((.*?)\)');
          final match = regex.firstMatch(taskTitle);
          if (match != null) {
            // Remove 'Request', 'Upload', commas, and 'Created for', but NOT 'for'
            var docType = match.group(1) ?? '';
            docType =
                docType
                    .replaceAll(
                      RegExp(
                        r'Request|Upload|,|Created for',
                        caseSensitive: false,
                      ),
                      '',
                    )
                    .trim();
            return docType.isNotEmpty ? docType : 'Document';
          }
        }
        return 'Document';
      case 'Letter request':
        return 'Letter';
      default:
        return category;
    }
  }

  String _mapCategoryToType(String taskDefinitionName) {
    switch (taskDefinitionName) {
      case 'AbsencesApprovalsTask':
        return 'Leave';
      case 'Compensation':
        return 'Claim';
      case 'DocumentOfRecordApproval':
        return 'Letter';
      case 'Talent':
        return 'Letter';
      default:
        return taskDefinitionName;
    }
  }
}
