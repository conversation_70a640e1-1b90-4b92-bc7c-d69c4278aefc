import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { HttpModule } from 'packages/http';
import { ExternalAuthInterceptor } from 'packages/http/externalauth.interceptor';
import { moduleExports } from './modules';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        process.env.NODE_ENV === 'production'
          ? '.env.production'
          : '.env.development',
    }),
    HttpModule.register({ baseURL: process.env.PMS_API_URL }),
    ...moduleExports,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ExternalAuthInterceptor,
    },
  ],
})
export class AppModule {}
