import { Injectable } from '@nestjs/common';
import { HttpModuleOptions, HttpService } from 'packages/http';
import { ParentModel } from '../models/parent/parent.model';
import { ParentDocumentDetailsModel } from '../models/parent/parentDocumentDetails.model';
import { ParentProfileModel } from '../models/parent/parentProfile.model';
import { ParentProfileUpdateModel } from '../models/parent/parentProfileUpdate.model';
import { ParentProfileVMModel } from '../models/parent/parentProfileVM.model';
import { ParentProfileWizardUpdateModel } from '../models/parent/parentProfileWizardUpdate.model';
import axios from 'axios';
import * as FormData from 'form-data';
import { ParentProfileWizardUpdateModel_2 } from '../models/parent/parentProfileWizardUpdate_2.model';
@Injectable()
export class ParentService {
  constructor(private readonly http: HttpService) {}

  public async getAppParentDetails(
    model: ParentProfileModel,
  ): Promise<ParentProfileModel> {
    // -- not used
    const response = await this.http.post<ParentProfileModel>(
      `dashboard/GetAppParentDetails`,
      model,
      {
    params: {
      parentId:model.parentId
    },
  }
    );
    return response;
  }

  public async createParent(model: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.http.post<ParentModel>(
      `parent/CreateParent`,
      model,
    );
    return response;
  }

  public async updateParent(model: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.http.post<ParentModel>(
      `parent/UpdateParent`,
      model,
    );
    return response;
  }

  public async updateParentDocumentDetails(
    model: ParentDocumentDetailsModel,
  ): Promise<ParentDocumentDetailsModel> {
    // -- not used
    const response = await this.http.post<ParentDocumentDetailsModel>(
      `parent/UpdateParentDocumentDetails`,
      model, {
      headers: {
        'Content-Type': 'application/json-patch+json',
      },
    }
    );
    return response;
  }

  public async updateParentProfile(
    model: ParentProfileVMModel,
  ): Promise<ParentProfileVMModel> {
    // -- not used
    const response = await this.http.post<ParentProfileVMModel>(
      `parent/UpdateParentProfile`,
      model,
    );
    return response;
  }



public async uploadFileToS3(file: Express.Multer.File): Promise<any> {

 const form = new FormData();
    form.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });

    const response = await this.http.post<any>(
      'upload/UploadFileToS3',
      form,
      {
        baseURL: process.env.NO_PARENT_PMS_API_URL,
        postHeaders: {
          'Content-Type': 'multipart/form-data'
        },
      },
    );
    return response;
}

public async uploadFileToS3WithOcr(file: Express.Multer.File): Promise<any> {

 const form = new FormData();
    form.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });

    const response = await this.http.post<any>(
      'upload/UploadFileToS3WithOcr',
      form,
      {
        baseURL: process.env.NO_PARENT_PMS_API_URL,
        postHeaders: {
          'Content-Type': 'multipart/form-data'
        },
      },
    );
    return response;
}


  public async deleteParent(model: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.http.post<ParentModel>(
      `parent/DeleteParent`,
      model,
    );
    return response;
  }

  public async getAllParents(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
  ): Promise<ParentProfileModel[]> {
    // -- not used
    const response = await this.http.get<ParentProfileModel[]>(
      `parent/GetAllParents`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
      },
      true,
    );
    return response;
  }

  public async getAllParentDetailsById(
    parentId?: string,
    fields?: string,
  ): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.http.get<ParentProfileVMModel[]>(
      `parent/GetAllParentDetalsById`,
      { parentId: parentId, Fields: fields },
      true,
    );
    return response;
  }

  public async getAllReviewedParentDetailsById(
    parentId?: number,
  ): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.http.get<ParentProfileVMModel[]>(
      `parent/GetAllReviewedParentDetalsById`,
      { parentId: parentId },
      true,
    );
    return response;
  }

  public async getAllNotVerifiedParents(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
  ): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.http.get<ParentProfileVMModel[]>(
      `parent/GetAllNotVeryfiedParents`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
      },
      true,
    );
    return response;
  }

  public async verifyParentEmiratesId(
    emiratesNo?: string,
    requestId?: string,
    oTP?: string,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.http.get<ParentModel>(
      `parent/VerifyParentEmiratesId`,
      { EmiratesNo: emiratesNo, requestId: requestId, OTP: oTP },
      false,
    );
    return response;
  }

  public async checkSpouseEmiratesId(
    emiratesNo?: string,
    familyId?: number,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.http.get<ParentModel>(
      `parent/CheckSpouseEmiratesId`,
      { EmiratesNo: emiratesNo, familyId: familyId },
      false,
    );
    return response;
  }

  public async checkParentEmiratesId(
    emiratesNo?: string,
    parentId?: number,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.http.get<ParentModel>(
      `parent/CheckParentEmiratesId`,
      { EmiratesNo: emiratesNo, parentId: parentId },
      false,
    );
    return response;
  }

  public async checkSpouseEmiratesIdByPass(
    emiratesNo?: string,
    familyId?: number,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.http.get<ParentModel>(
      `parent/CheckSpouseEmiratesIdByPass`,
      { EmiratesNo: emiratesNo, familyId: familyId },
      false,
    );
    return response;
  }

  public async verifyParentEmiratesIdByPass(
    emiratesNo?: string,
    requestId?: string,
    oTP?: string,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.http.get<ParentModel>(
      `parent/VerifyParentEmiratesIdByPass`,
      { EmiratesNo: emiratesNo, requestId: requestId, OTP: oTP },
      false,
    );
    return response;
  }

  public async updateParentProfileForWizard(
  model: ParentProfileWizardUpdateModel,
): Promise<ParentProfileWizardUpdateModel> {
  try {
    const response = await this.http.post<ParentProfileWizardUpdateModel>(
      `parent/UpdateParentProfileForWizard`,
      model,
      {
        baseURL: process.env.NO_PARENT_PMS_API_URL,
      }
    );
    return response;
  } catch (error) {
    console.error('Error updating parent profile for wizard:', error);

    // You can throw a custom error or rethrow original
    throw new Error(
      (error as any)?.response?.data?.message || 'Failed to update profile.'
    );
  }
}

  public async updateParentProfileForWizard_2(
    model: ParentProfileWizardUpdateModel_2,
  ): Promise<ParentProfileWizardUpdateModel_2> {
    try {
      const response = await this.http.post<ParentProfileWizardUpdateModel_2>(
        `parent/UpdateParentProfileForWizard`,
        model,
        {
          baseURL: process.env.NO_PARENT_PMS_API_URL,

        } 
      );
      return response;
    } catch (error) {
      console.error('Error updating parent profile for wizard:', error);

      // You can throw a custom error or rethrow original
      throw new Error(
        (error as any)?.response?.data?.message || 'Failed to update profile.'
      );
    }
  }


  public async parentProfileUpdate(
    model: ParentProfileUpdateModel,
  ): Promise<ParentProfileUpdateModel> {
    // -- not used
    const response = await this.http.post<ParentProfileUpdateModel>(
      `parent/ParentProfileUpdate`,
      model,
    );
    return response;
  }

  public async getAllParentDetails(): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.http.get<ParentProfileVMModel[]>(
      `parent/GetllParentDetails`,
      {},
      true,
    );
    return response;
  }

  public async getLoggedInParentMenusData(
    parentId?: number,
    familyId?: number,
    kidId?: number,
  ): Promise<ParentProfileModel[]> {
    // -- not used
    
    const response = await this.http.get<ParentProfileModel[]>(
      `parentmenu/getLoggedInParentMenusData`,
      { parentId: parentId, familyId: familyId, kidId: kidId },
      true,
      {
        baseURL: process.env.NO_PARENT_PMS_API_URL,
      },
    );
    return response;
  }
}
