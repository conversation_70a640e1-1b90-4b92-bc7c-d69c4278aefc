/* eslint-disable prettier/prettier */
import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import {
  JwtPayloadWithUser,
  RequestWithUser,
} from 'apps/auth-api/src/auth/jwt-payload.interface';
import { ActionsService } from 'apps/ssr-api/src/providers/services/actions.service';
import { AttachmentService } from 'apps/ssr-api/src/providers/services/attachment.service';
import { WorkflowtargetService } from 'apps/ssr-api/src/providers/services/workflowtarget.service';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { TasksModel } from '../providers/models/tasks/tasks.model';
import { TasksChangedAttributesItemResponseModel } from '../providers/models/tasks/tasksChangedAttributesItemResponse.model';
import { TasksItemResponseModel } from '../providers/models/tasks/tasksItemResponse.model';
import { TasksService } from '../providers/services/tasks.service';
@Controller()
export class TasksController {
  constructor(
    private readonly service: TasksService,
    private readonly actionsService: ActionsService,
    private readonly workflowService: WorkflowtargetService,
    private readonly attachmentService: AttachmentService,
  ) {}

  @Get(
    'tasks/:identificationKey/child/changedAttributes/:changedAttributesUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TasksChangedAttributesItemResponseModel })
  public async getChangedAttributesById(
    @Query() query?: any,
  ): Promise<TasksChangedAttributesItemResponseModel> {
    // -- not used
    const {
      identificationKey,
      changedAttributesUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getChangedAttributesById(
      identificationKey,
      changedAttributesUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('tasks/:identificationKey')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksItemResponseModel> })
  public async getTasksById(
    @Query() query?: any,
    @Param('identificationKey') identificationKey?: string,
  ): Promise<TasksItemResponseModel[]> {
    // -- not used
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getTasksById(
      identificationKey,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  // Helper to fetch all tasks in batches if totalResults > 500
  private async fetchAllTasksWithOffset(
    service: TasksService,
    params: any,
    status?: string,
    assignment?: string,
  ) {
    const limit = 500;
    let offset = 0;
    let allItems: TasksItemResponseModel[] = [];
    let totalResults = 0;
    let firstResponse: TasksItemResponseModel[] = [];
    // "dueDate": "2025-12-21 13:26:59",
    // const minDate = new Date();
    //minDate.setMonth(minDate.getMonth() - 2);
    do {
      const response = await service.getTasks(
        params.expand,
        params.identificationKey,
        assignment ?? 'ALL',
        params.fields,
        params.onlyData,
        params.links,
        limit,
        offset,
        params.totalResults,
        params.q,
        status,
        params.orderBy,
        params.finder,
        params.fromuser,
        // minDate.toISOString().split('T')[0],
      );
      if (!firstResponse) firstResponse = response.items;
      if (response.items && response.items.length > 0) {
        allItems = allItems.concat(response.items);
      }
      totalResults = response.totalResults || 0;
      offset += limit;
    } while (totalResults > offset);

    if (status === 'ASSIGNED') {
      //
      // const itsmApprovalTasks = await this.workflowService.getTicketApproval({
      //   include_attachments: true,
      // });
      // console.log(`Fetching ITSM approval tasks: ${itsmApprovalTasks.length}`);
    }

    if (firstResponse) {
      firstResponse = allItems;
    }
    return firstResponse;
  }

  private statusGroups = (req?: RequestWithUser) => {
    console.log(req?.user?.fusionProfile?.UserName);
    return {
      ASSIGNED: [
        { status: 'ASSIGNED' },
        //  { status: 'EXPIRED', fromuser: req?.user?.fusionProfile?.UserName },// approval not working for expired tasks from some accounts
      ],
      COMPLETED: [{ status: 'COMPLETED' }],
      SUSPENDED: [{ status: 'SUSPENDED' }],
      WITHDRAWN: [{ status: 'WITHDRAWN' }],
      // ERRORED: ['ERRORED'],
      // ALERTED: ['ALERTED'],
    };
  };

  @Get('tasksCount')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksModel> })
  public async getTasksCount(
    @Query() query?: any,
    @Req() req?: RequestWithUser,
  ): Promise<any> {
    const {
      expand,
      fields,
      identificationKey,
      onlyData,
      links,
      totalResults,
      q,
      // orderBy,
      finder,
      forAbsences,
      forDocumentRecords
    } = query;

    const statusGroups = this.statusGroups(req);

    const responseObj: any = {};
    for (const groupKey in statusGroups) {
      const groupItems = statusGroups[groupKey];
      let allItems: TasksItemResponseModel[] = [];
      // let totalResultsSum = 0;
      if (groupKey == 'ASSIGNED') {
        console.log(
          `Fetching tasks for ASSIGNED group with status: ${groupKey}`,
        );
      }
      for (const group of groupItems) {
        const statusParam = group.status;
        const response = await this.fetchAllTasksWithOffset(
          this.service,
          {
            expand,
            identificationKey,
            fields,
            onlyData,
            links,
            totalResults,
            q,
            // orderBy: orderBy || 'createdDate:desc',
            finder,
            fromuser: group.fromuser,
          },
          statusParam,
        );

        allItems = allItems.concat(response || []);
      }

      // Apply group-level logic (example for ASSIGNED)

      if (forAbsences) {
        allItems = this.sortTasksByDate(allItems);
        allItems = this.filterOnlyByAbsencesTasks(allItems);
        allItems = this.filterTasksByStatusAndAssignment(allItems, req?.user);
      } else if (forDocumentRecords) {
        allItems = this.sortTasksByDate(allItems);
        allItems = this.filterOnlyByDocumentRecordsTasks(allItems);
        allItems = this.filterTasksByStatusAndAssignment(allItems, req?.user);
      } else {
        allItems = this.sortTasksByDate(allItems);
        allItems = this.filterTasksByStatusAndAssignment(allItems, req?.user);
        const allItemsCopy = [...allItems];
        allItems = allItems.filter((task) => this.filterAllowedTasks(task));

        const excludedTasks = allItemsCopy.filter(
          (task) => allItems.indexOf(task) === -1,
        );
        console.log(
          `Excluded tasks from ASSIGNED group: ${excludedTasks.length}`,
        );
      }

      // Set the combined response for the group
      responseObj[groupKey] = new TasksModel();
      responseObj[groupKey].totalResults = allItems.length;
      responseObj[groupKey].items = [];
    }
    return responseObj;
  }

  private filterTasksByStatusAndAssignment(
    tasks: TasksItemResponseModel[],
    user?: JwtPayloadWithUser,
  ): TasksItemResponseModel[] {
    const myUserName = user?.fusionProfile?.UserName;
    tasks = tasks.filter(
      (task) =>
        (task.fromUserName?.toLowerCase() === myUserName?.toLocaleLowerCase() &&
          task.state === 'EXPIRED' &&
          task.createdBy.toLowerCase() != user?.name.toLowerCase()) ||
        (task.assignees?.items?.length > 0 &&
          task.assignees.items.filter(
            (assignee) => assignee.id === myUserName?.toLocaleLowerCase(),
          ).length > 0),
    );
    return tasks;
  }

  private filterAllowedTasks(item: TasksItemResponseModel): boolean {
    return (
      (item.taskDefinitionName === 'DocumentOfRecordApproval' ||
        item.taskDefinitionName === 'AbsencesApprovalsTask' ||
        item.taskDefinitionName === 'VariableAllocationTask') &&
      // item.taskDefinitionName === 'PerfDocApprovalTask'
      item.taskDefinitionName.indexOf('Fyi') === -1 &&
      item.taskDefinitionName.indexOf('FYI') === -1 &&
      (!item.titlePrefix ||
        ((item.state === 'EXPIRED' || item.state === 'ASSIGNED') &&
          item.titlePrefix === 'Action Required') ||
        (item.state === 'WITHDRAWN' && item.titlePrefix === 'Withdrawn'))
    );
  }

  private filterOnlyByAbsencesTasks(
    tasks: TasksItemResponseModel[],
  ): TasksItemResponseModel[] {
    return tasks.filter(
      (task) => task.taskDefinitionName === 'AbsencesApprovalsTask',
    );
  }

  private filterOnlyByDocumentRecordsTasks(
    tasks: TasksItemResponseModel[],
  ): TasksItemResponseModel[] {
    const allowedTypes = [
      'Request NOC for Drivers License',
      'Request Bank Change',
      'Request Business Card',
      'Request Embassy Letter',
      'Request Medical Fitness Letter',
      'Request Residency Letter',
      'Request Salary Certificate',
      'Request Salary Transfer',
    ];

    return tasks.filter((task) =>
      task.taskDefinitionName === 'DocumentOfRecordApproval' &&
      allowedTypes.some(keyword => task.title?.includes(keyword))
    );
  }


  private sortTasksByDate(
    tasks: TasksItemResponseModel[],
  ): TasksItemResponseModel[] {
    return tasks.sort((a, b) => {
      const aDate = a.assignedDate ? new Date(a.assignedDate) : new Date(0);
      const bDate = b.assignedDate ? new Date(b.assignedDate) : new Date(0);
      return bDate.getTime() - aDate.getTime();
    });
  }

  @Get('tasks')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksModel> })
  public async getTasks(
    @Query() query?: any,
    @Req() req?: RequestWithUser,
  ): Promise<TasksModel> {
    const {
      expand,
      identificationKey,
      assignment,
      fields,
      forApproval,
      forAbsences,
      onlyData,
      links,
      totalResults,
      q,
      status,
      // orderBy,
      finder,
      startDate,
      endDate,
      search,
      forDocumentRecords
    } = query;
    const dateFilter: boolean =
      startDate != null && startDate !== '' && endDate != null && endDate !== ''
        ? true
        : false;
    console.log('[EMS] Fetching tasks with filters:', dateFilter);

    const taskGroups = this.statusGroups(req);

    // Example: fetch and combine all statuses in the ASSIGNED group
    let allItems: TasksItemResponseModel[] = [];
    for (const group of taskGroups[status || 'ASSIGNED']) {
      if (status == 'ASSIGNED') {
        console.log(
          `Fetching tasks for ASSIGNED group with status: ${group.status}`,
        );
      }

      const statusParam = group.status;
      const groupItems = await this.fetchAllTasksWithOffset(
        this.service,
        {
          expand,
          identificationKey,
          fields,
          onlyData,
          links,
          totalResults,
          q,
          // orderBy: orderBy || 'createdDate:desc',
          finder,
          fromuser: group.fromuser,
        },
        statusParam,
        assignment,
      );
      allItems = allItems.concat(groupItems || []);
    }

    // Now allItems contains all ASSIGNED and EXPIRED tasks (or whatever group you want)
    let response = allItems;

    // ...existing forApproval, forAbsences, date, and search filtering logic...
    if (forApproval) {
      response = this.sortTasksByDate(response);
      response = this.filterTasksByStatusAndAssignment(response, req?.user);
      response = response.filter((task) => this.filterAllowedTasks(task));
    } else if (forAbsences) {
      response = this.sortTasksByDate(response);
      response = this.filterTasksByStatusAndAssignment(response, req?.user);
      response = this.filterOnlyByAbsencesTasks(response);
    } else if (forDocumentRecords) {
      response = this.sortTasksByDate(response);
      response = this.filterTasksByStatusAndAssignment(response, req?.user);
      response = this.filterOnlyByDocumentRecordsTasks(response);
    }

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      response = response.filter((task) => {
        if (!task.createdDate) return false;
        const createdDate = new Date(task.createdDate);
        return createdDate >= start && createdDate <= end;
      });
    }
    if (search) {
      response = response.filter((item) => {
        return (
          (item.createdBy?.toLowerCase() ?? '').includes(
            search.toLowerCase(),
          ) ||
          (item.title?.toLowerCase() ?? '').includes(search.toLowerCase()) ||
          (item.taskDefinitionName?.toLowerCase() ?? '').includes(
            search.toLowerCase(),
          ) ||
          (item.category?.toLowerCase() ?? '').includes(search.toLowerCase())
        );
      });
    }
    const tasksModel = new TasksModel();
    tasksModel.totalResults = response.length;
    tasksModel.items = response;
    tasksModel.offset = 0;
    tasksModel.hasMore = false;
    tasksModel.limit = response.length;
    tasksModel.count = response.length;
    return tasksModel;
  }

  @Get('tasks/:id/history')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: [TasksModel] })
  public async getTask(
    @Param('id') id: string,
    @Query() query?: any,
  ): Promise<TasksModel[]> {
    // -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.getTask(id);
    return response;
  }

  @Put('tasks')
  // @UsePipes(new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Object })
  public async putTasks(
    @Body('tasks') taskIds: string[],
    @Body('action') action: { id: string },
    @Body('comment') comment: { commentStr: string },
    @Req() req?: RequestWithUser,
  ): Promise<any> {
    // Extract username from fusion profile
    const username = req?.user?.fusionProfile?.UserName;

    return this.service.putTasks(
      taskIds,
      action.id,
      comment.commentStr,
      username ? username.toLowerCase() : '',
    );
  }
}
