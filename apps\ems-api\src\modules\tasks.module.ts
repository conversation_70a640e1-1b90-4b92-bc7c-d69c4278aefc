import { Module } from '@nestjs/common';
import { ActionsService } from 'apps/ssr-api/src/providers/services/actions.service';
import { AttachmentService } from 'apps/ssr-api/src/providers/services/attachment.service';
import { WorkflowtargetService } from 'apps/ssr-api/src/providers/services/workflowtarget.service';
import { HttpModule } from 'packages/http';
import { TasksController } from '../controllers/tasks.controller';
import { TasksService } from '../providers/services/tasks.service';
@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL2,
      authTokenParams: {
        oracleCustomToken: true,
      },
      apiTenant: 'ems',
    }),
  ],
  controllers: [TasksController],
  providers: [
    TasksService,
    WorkflowtargetService,
    ActionsService,
    AttachmentService,
  ],
})
export class TasksModule {}
