// token-refresh.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NextFunction, Request, Response } from 'express';
import * as jwt from 'jsonwebtoken';
import { UserProfile } from '../../entities/userProfile.entity';
import { AuthService } from '../auth.service';
import { JwtPayload, JwtPayloadWithUser } from '../jwt-payload.interface';

interface AuthenticatedRequest extends Request {
  user?: JwtPayloadWithUser;
  accessTokenRefreshed?: boolean;
  newJwtToken?: string; // Add this to pass new JWT to response
}

@Injectable()
export class TokenRefreshMiddleware implements NestMiddleware {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      // Extract JWT token from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next();
      }

      const extractedJwtToken = authHeader.split(' ')[1];
      if (!extractedJwtToken) {
        return next();
      }

      // Decode JWT to get user info and token expiration
      let decodedToken: JwtPayload;
      try {
        decodedToken = jwt.decode(extractedJwtToken) as JwtPayload;
      } catch {
        return next();
      }

      if (!decodedToken || !decodedToken.userId) {
        return next();
      }

      // const userId = decodedToken.userId;
      // const currentTime = Math.floor(Date.now() / 1000);
      // const tokenExpiresAt = decodedToken.adExpiresIn;
      // const expirationBuffer = 300; // 5 minutes
      // const willExpireSoon = tokenExpiresAt <= currentTime + expirationBuffer;

      // if (willExpireSoon) {
      //   // Check if user has refresh token
      //   const refreshToken = decodedToken.adRefreshToken;
      //   if (
      //     !refreshToken ||
      //     refreshToken === 'not available' ||
      //     refreshToken === ''
      //   ) {
      //     return next();
      //   }

      //   try {
      //     // Call Microsoft to get new access token
      //     const newTokenData =
      //       await this.authService.refreshAccessToken(refreshToken);

      //     if (newTokenData && newTokenData.access_token) {
      //       // Update the access token in database and get new JWT
      //       const newJwtToken = await this.updateUserTokenAndGenerateNewJWT(
      //         userId,
      //         newTokenData,
      //         decodedToken,
      //       );

      //       if (newJwtToken) {
      //         // Mark that token was refreshed and include new JWT
      //         req.accessTokenRefreshed = true;
      //         req.newJwtToken = newJwtToken;
      //       }
      //     }
      //   } catch {
      //     // Intentionally ignored: token refresh failed
      //   }
      // }

      // Add user info to request for downstream use
      let fusionProfile: UserProfile = new UserProfile();
      if (decodedToken.fusionProfile) {
        try {
          fusionProfile =
            typeof decodedToken.fusionProfile === 'string'
              ? JSON.parse(decodedToken.fusionProfile)
              : decodedToken.fusionProfile;
        } catch (e) {
          console.error('Error parsing fusionProfile:', e);
        }
      }

      let halloProfile: UserProfile = new UserProfile();
      if (decodedToken.halloProfile) {
        try {
          halloProfile =
            typeof decodedToken.halloProfile === 'string'
              ? JSON.parse(decodedToken.halloProfile)
              : decodedToken.halloProfile;
        } catch (e) {
          console.error('Error parsing halloProfile:', e);
        }
      }

      let nmsProfile: UserProfile = new UserProfile();
      if (decodedToken.nmsProfile) {
        try {
          nmsProfile =
            typeof decodedToken.nmsProfile === 'string'
              ? JSON.parse(decodedToken.nmsProfile)
              : decodedToken.nmsProfile;
        } catch (e) {
          console.error('Error parsing nmsProfile:', e);
        }
      }

      let pmsProfile: UserProfile = new UserProfile();
      if (decodedToken.pmsProfile) {
        try {
          pmsProfile =
            typeof decodedToken.pmsProfile === 'string'
              ? JSON.parse(decodedToken.pmsProfile)
              : decodedToken.pmsProfile;
        } catch (e) {
          console.error('Error parsing pmsProfile:', e);
        }
      }

      req.user = {
        ...decodedToken,
        fusionProfile,
        halloProfile,
        nmsProfile,
        pmsProfile,
      };
    } catch (error) {
      console.log('❌ Error in token refresh middleware:', error.message);
    }

    // Override res.json to include new token in response
    // const originalJson = res.json.bind(res);
    // res.json = function (body: any) {
    //   if (req.accessTokenRefreshed && req.newJwtToken) {
    //     // Add new token to response headers
    //     res.setHeader('X-New-Auth-Token', req.newJwtToken);

    //     // Also include in response body if it's an object
    //     if (typeof body === 'object' && body !== null) {
    //       body.newAuthToken = req.newJwtToken;
    //       body.tokenRefreshed = true;
    //     }
    //   }
    //   return originalJson(body);
    // };
    next();
  }

  // Update your updateUserTokenAndGenerateNewJWT method in token-refresh.middleware.ts

  // private async updateUserTokenAndGenerateNewJWT(
  //   userId: number,
  //   newTokenData: any,
  //   oldDecodedToken: any,
  // ): Promise<string | null> {
  //   try {
  //     // Find the user's current Office365 token record
  //     const existingToken =
  //       await this.authService.findUserOffice365Token(userId);

  //     if (existingToken) {
  //       // Create update data object
  //       const updateData: {
  //         accessToken: string;
  //         expiresIn: number;
  //         refreshToken?: string;
  //       } = {
  //         accessToken: newTokenData.access_token,
  //         expiresIn:
  //           Math.floor(Date.now() / 1000) + (newTokenData.expires_in || 3600),
  //       };

  //       // Also update refresh token if a new one was provided
  //       if (newTokenData.refresh_token) {
  //         updateData.refreshToken = newTokenData.refresh_token;
  //       }

  //       await this.authService.updateTokenRecord(existingToken.id, updateData);

  //       // Generate new JWT with updated token info
  //       const newJwtPayload = {
  //         ...oldDecodedToken,
  //         adToken: newTokenData.access_token,
  //         adExpiresIn: updateData.expiresIn,
  //         adRefreshToken:
  //           newTokenData.refresh_token || oldDecodedToken.adRefreshToken,
  //         iat: Math.floor(Date.now() / 1000), // Update issued at time
  //       };

  //       // IMPORTANT: Remove 'exp' from payload to avoid conflict
  //       delete newJwtPayload.exp;

  //       const tokenSecret =
  //         this.configService.get<string>('TOKEN_SECRET') || '12345';
  //       const newJwtToken = jwt.sign(newJwtPayload, tokenSecret, {
  //         expiresIn: '10y', // This will set the exp claim
  //       });

  //       return newJwtToken;
  //     } else {
  //       return null;
  //     }
  //   } catch (error) {
  //     console.error('Error updating user token and generating new JWT:', error);
  //     return null;
  //   }
  // }
}
